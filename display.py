#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Display module for BMC Remedy Container application.
Handles console output formatting and display logic separate from business logic.
"""

import json
import os
from typing import Dict, List, Any, Optional
from prettytable import PrettyTable
from colorama import init, Fore, Style

from config import is_silent_mode
from logging_config import get_logger

# Inicializar colorama para colores en la consola
init()


class ReportDisplayer:
    """Handles display of BMC Remedy reports with proper logging integration."""
    
    def __init__(self, enable_colors: bool = True, silent_mode: bool = False):
        """
        Initialize the report displayer.
        
        Args:
            enable_colors: Whether to enable colored output
            silent_mode: Whether to suppress console output
        """
        self.enable_colors = enable_colors
        self.silent_mode = silent_mode
        self.logger = get_logger('display')
    
    def print_colored_status(self, status: str) -> str:
        """
        Format status with appropriate color.
        
        Args:
            status: Status string to format
            
        Returns:
            Formatted status string with colors (if enabled)
        """
        if not self.enable_colors or not status or not isinstance(status, str):
            return status
            
        status_lower = status.lower()
        if status_lower in ['resuelto', 'resolved', 'closed', 'baja', 'low']:
            return f"{Fore.GREEN}{status}{Style.RESET_ALL}"
        elif status_lower in ['pendiente', 'pending', 'media', 'medium']:
            return f"{Fore.YELLOW}{status}{Style.RESET_ALL}"
        elif status_lower in ['abierto', 'assigned', 'open', 'alta', 'high']:
            return f"{Fore.RED}{status}{Style.RESET_ALL}"
        
        return status
    
    def format_value(self, value: Any) -> str:
        """
        Format a value for display.
        
        Args:
            value: Value to format
            
        Returns:
            Formatted string representation
        """
        if value is None:
            return "N/A"
        elif isinstance(value, dict):
            return ", ".join([f"{k}: {v}" for k, v in value.items() if v is not None and v != "N/A"])
        elif value == "":
            return "N/A"
        return str(value)
    
    def display_section_header(self, title: str):
        """
        Display a section header.
        
        Args:
            title: Section title
        """
        if self.silent_mode:
            return
            
        if self.enable_colors:
            formatted_title = f"\n{Fore.CYAN}=== {title} ==={Style.RESET_ALL}"
        else:
            formatted_title = f"\n=== {title} ==="
        
        print(formatted_title)
        self.logger.info(title)
    
    def display_info_line(self, label: str, value: Any):
        """
        Display an information line.
        
        Args:
            label: Label for the information
            value: Value to display
        """
        if self.silent_mode:
            return
            
        line = f"{label}: {value}"
        print(line)
        self.logger.info(line)
    
    def display_item_header(self, item_type: str, index: int, item_id: str):
        """
        Display an item header (ticket/incident).
        
        Args:
            item_type: Type of item (Ticket, Incidente)
            index: Item index
            item_id: Item ID
        """
        if self.silent_mode:
            return
            
        if self.enable_colors:
            header = f"\n{Fore.CYAN}{item_type} #{index}: {item_id}{Style.RESET_ALL}"
        else:
            header = f"\n{item_type} #{index}: {item_id}"
        
        print(header)
        self.logger.info(f"{item_type} #{index}: {item_id}")
    
    def display_table(self, table: PrettyTable):
        """
        Display a formatted table.
        
        Args:
            table: PrettyTable instance to display
        """
        if self.silent_mode:
            return
            
        print(table)
        # Log table content in a structured way
        self.logger.debug(f"Displaying table with {len(table._rows)} rows")
    
    def display_note_header(self):
        """Display header for notes section."""
        if self.silent_mode:
            return
            
        if self.enable_colors:
            header = f"\n{Fore.CYAN}Última nota:{Style.RESET_ALL}"
        else:
            header = "\nÚltima nota:"
        
        print(header)
        self.logger.info("Displaying last note")
    
    def display_warning(self, message: str):
        """
        Display a warning message.
        
        Args:
            message: Warning message
        """
        if self.silent_mode:
            return
            
        if self.enable_colors:
            formatted_message = f"\n{Fore.YELLOW}{message}{Style.RESET_ALL}"
        else:
            formatted_message = f"\n{message}"
        
        print(formatted_message)
        self.logger.warning(message)
    
    def display_error(self, message: str):
        """
        Display an error message.
        
        Args:
            message: Error message
        """
        if self.enable_colors:
            formatted_message = f"{Fore.RED}Error: {message}{Style.RESET_ALL}"
        else:
            formatted_message = f"Error: {message}"
        
        print(formatted_message)
        self.logger.error(message)
    
    def create_item_table(self, item: Dict[str, Any], priority_fields: List[str]) -> PrettyTable:
        """
        Create a formatted table for an item (ticket/incident).
        
        Args:
            item: Item data dictionary
            priority_fields: List of fields to display first
            
        Returns:
            Formatted PrettyTable
        """
        table = PrettyTable()
        table.field_names = ["Campo", "Valor"]
        table.align["Campo"] = "l"
        table.align["Valor"] = "l"
        table.max_width["Valor"] = 60
        
        # Add priority fields first
        for key in priority_fields:
            if key in item:
                value = item[key]
                formatted_value = self.format_value(value)
                
                if key in ["status", "priority"]:
                    formatted_value = self.print_colored_status(formatted_value)
                
                # Field name mapping
                field_name = self._get_field_display_name(key)
                table.add_row([field_name, formatted_value])
        
        # Add remaining fields
        for key, value in item.items():
            if key not in priority_fields and key != 'last_note':
                formatted_value = self.format_value(value)
                
                if key in ["status", "priority"]:
                    formatted_value = self.print_colored_status(formatted_value)
                
                field_name = key.replace("_", " ").title()
                table.add_row([field_name, formatted_value])
        
        return table
    
    def create_note_table(self, note: Dict[str, Any]) -> PrettyTable:
        """
        Create a formatted table for a note.
        
        Args:
            note: Note data dictionary
            
        Returns:
            Formatted PrettyTable
        """
        table = PrettyTable()
        table.field_names = ["Campo", "Valor"]
        table.align["Campo"] = "l"
        table.align["Valor"] = "l"
        table.max_width["Valor"] = 60
        
        for note_key, note_value in note.items():
            formatted_note_value = self.format_value(note_value)
            
            # Field name mapping for notes
            note_field_name = {
                "summary": "Resumen",
                "detailed_description": "Descripción Detallada",
                "note_number": "Número de Nota",
                "last_modified_date": "Fecha de Modificación"
            }.get(note_key, note_key.replace("_", " ").title())
            
            table.add_row([note_field_name, formatted_note_value])
        
        return table
    
    def _get_field_display_name(self, key: str) -> str:
        """
        Get display name for a field key.
        
        Args:
            key: Field key
            
        Returns:
            Display name for the field
        """
        field_mapping = {
            "request_id": "Work Order ID",
            "incident_id": "Incident ID",
            "summary": "Resumen",
            "description": "Descripción",
            "detailed_description": "Descripción Detallada",
            "technical_info": "Información Técnica",
            "status": "Estado",
            "dwp_number": "Número DWP",
            "priority": "Prioridad"
        }
        
        return field_mapping.get(key, key.replace("_", " ").title())


def create_displayer(enable_colors: bool = True) -> ReportDisplayer:
    """
    Create a report displayer instance.
    
    Args:
        enable_colors: Whether to enable colored output
        
    Returns:
        ReportDisplayer instance
    """
    return ReportDisplayer(
        enable_colors=enable_colors,
        silent_mode=is_silent_mode()
    )
