#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script de prueba para verificar la conversión de fechas a hora colombiana
"""

from datetime import datetime, timedelta

def formatear_fecha_colombia(fecha_str):
    """Formatea una fecha de formato ISO a formato DD/MM/YYYY HH:MM en hora colombiana (UTC-5)"""
    if not fecha_str or fecha_str == "N/A":
        return "N/A"
    
    try:
        # Parsear la fecha ISO (ej: 2025-02-19T07:00:08.000+0000)
        if 'T' in fecha_str:
            fecha_parte = fecha_str.split('T')[0]
            hora_parte = fecha_str.split('T')[1].split('.')[0] if '.' in fecha_str else fecha_str.split('T')[1].split('+')[0]
            
            # Convertir a datetime (asumiendo que viene en UTC)
            fecha_obj = datetime.strptime(f"{fecha_parte} {hora_parte}", "%Y-%m-%d %H:%M:%S")
            
            # Convertir a hora colombiana (UTC-5)
            fecha_colombia = fecha_obj - timedelta(hours=5)
            
            # Formatear como DD/MM/YYYY HH:MM (Hora Colombia)
            return fecha_colombia.strftime("%d/%m/%Y %H:%M (COL)")
        else:
            return fecha_str
    except Exception as e:
        print(f"Error al formatear fecha: {e}")
        return fecha_str

def test_conversion_fechas():
    """Prueba la conversión de fechas de UTC a hora colombiana"""
    
    print("=== PRUEBA DE CONVERSIÓN DE FECHAS A HORA COLOMBIANA ===\n")
    
    # Fechas de ejemplo del reporte actual
    fechas_prueba = [
        "2025-02-16T01:29:50.000+0000",  # Ejemplo del incidente 1
        "2025-04-22T20:49:28.000+0000",  # Ejemplo del incidente 2
        "2025-03-17T14:17:55.000+0000",  # Ejemplo de nota
        "2025-06-16T12:58:50.000+0000",  # Ejemplo del incidente 6
        "N/A",                           # Caso sin fecha
        "2025-06-26T05:00:00.000+0000",  # Fecha de prueba (medianoche Colombia)
    ]
    
    for fecha_utc in fechas_prueba:
        fecha_colombia = formatear_fecha_colombia(fecha_utc)
        print(f"UTC:      {fecha_utc}")
        print(f"Colombia: {fecha_colombia}")
        print("-" * 50)
    
    print("\n=== EXPLICACIÓN ===")
    print("Colombia está en UTC-5, por lo que:")
    print("- Las fechas se reducen en 5 horas")
    print("- Ejemplo: 01:29 UTC = 20:29 COL (día anterior)")
    print("- Ejemplo: 14:17 UTC = 09:17 COL (mismo día)")
    print("- Se agrega '(COL)' para indicar hora colombiana")

if __name__ == "__main__":
    test_conversion_fechas()
