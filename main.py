#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script principal para automatizar el flujo completo de:
1. Buscar casos en BMC Remedy
2. Generar reporte y enviarlo por correo

Uso:
    python main.py <cedula> [--destinatario <email>] [--todos] [--silent] [--debug]
"""

import sys
import os
import subprocess
import argparse
import json
from datetime import datetime
from pathlib import Path
from typing import Optional, Tuple

# Import custom modules
from config import get_config, setup_from_args, is_silent_mode
from logging_config import setup_logging, get_logger, log_operation_start, log_operation_success, log_operation_error

def ejecutar_flujo_completo(cedula: str, destinatario: Optional[str] = None, todos: bool = False) -> bool:
    """
    Ejecuta el flujo completo de búsqueda de casos y envío de reporte

    Args:
        cedula: Número de cédula a consultar
        destinatario: Correo del destinatario (opcional)
        todos: Si es True, muestra todos los incidentes

    Returns:
        True si el proceso se completó correctamente, False en caso contrario
    """
    logger = get_logger('main.flujo_completo')

    try:
        log_operation_start(logger, "flujo completo", cedula=cedula, destinatario=destinatario, todos=todos)

        # Paso 1: Ejecutar open.py para buscar casos en BMC Remedy
        success, tickets_count, incidentes_count = _ejecutar_busqueda_casos(cedula, todos, logger)
        if not success:
            return False

        # Paso 2: Enviar el reporte por correo usando procesar_todo.py
        success = _ejecutar_envio_reporte(cedula, destinatario, logger)
        if not success:
            return False

        log_operation_success(
            logger, "flujo completo",
            cedula=cedula,
            tickets_encontrados=tickets_count,
            incidentes_encontrados=incidentes_count
        )
        return True

    except Exception as e:
        log_operation_error(logger, "flujo completo", e, cedula=cedula)
        return False


def _ejecutar_busqueda_casos(cedula: str, todos: bool, logger) -> Tuple[bool, int, int]:
    """
    Ejecuta la búsqueda de casos en BMC Remedy.

    Args:
        cedula: Número de cédula a consultar
        todos: Si es True, muestra todos los incidentes
        logger: Logger instance

    Returns:
        Tuple of (success, tickets_count, incidents_count)
    """
    log_operation_start(logger, "búsqueda de casos", cedula=cedula, mostrar_todos=todos)

    # Construir el comando para open.py
    comando_open = [sys.executable, 'open.py', cedula]
    if todos:
        comando_open.append('--todos')

    # Ejecutar open.py
    logger.info(f"Ejecutando comando: {' '.join(comando_open)}")
    resultado_open = subprocess.run(
        comando_open,
        check=False,
        capture_output=True,
        text=True,
        encoding='utf-8'
    )

    if resultado_open.returncode != 0:
        logger.error(f"Error al ejecutar open.py (código {resultado_open.returncode})")
        if resultado_open.stderr:
            logger.error(f"Error stderr: {resultado_open.stderr}")
        return False, 0, 0

    # Log the output from open.py if not in silent mode
    if not is_silent_mode() and resultado_open.stdout:
        logger.info("Resultado de la búsqueda de casos:")
        # Log each line separately to maintain formatting
        for line in resultado_open.stdout.strip().split('\n'):
            if line.strip():
                logger.info(line)

    # Verificar si se encontraron casos revisando el archivo JSON generado
    tickets_count, incidentes_count = _contar_casos_encontrados(logger)

    log_operation_success(
        logger, "búsqueda de casos",
        tickets_encontrados=tickets_count,
        incidentes_encontrados=incidentes_count
    )

    return True, tickets_count, incidentes_count


def _ejecutar_envio_reporte(cedula: str, destinatario: Optional[str], logger) -> bool:
    """
    Ejecuta el envío del reporte por correo.

    Args:
        cedula: Número de cédula
        destinatario: Correo del destinatario
        logger: Logger instance

    Returns:
        True si el envío fue exitoso, False en caso contrario
    """
    log_operation_start(logger, "envío de reporte", cedula=cedula, destinatario=destinatario)

    # Construir el comando para procesar_todo.py
    comando_procesar = [sys.executable, 'procesar_todo.py', cedula]
    if destinatario:
        comando_procesar.extend(['--destinatario', destinatario])

    # Ejecutar procesar_todo.py
    logger.info(f"Ejecutando comando: {' '.join(comando_procesar)}")
    resultado_procesar = subprocess.run(
        comando_procesar,
        check=False,
        capture_output=True,
        text=True,
        encoding='utf-8'
    )

    if resultado_procesar.returncode != 0:
        logger.error(f"Error al ejecutar procesar_todo.py (código {resultado_procesar.returncode})")
        if resultado_procesar.stderr:
            logger.error(f"Error stderr: {resultado_procesar.stderr}")
        return False

    # Log the output from procesar_todo.py if not in silent mode
    if not is_silent_mode() and resultado_procesar.stdout:
        # Log each line separately to maintain formatting
        for line in resultado_procesar.stdout.strip().split('\n'):
            if line.strip():
                logger.info(line)

    log_operation_success(logger, "envío de reporte", destinatario=destinatario)
    return True


def _contar_casos_encontrados(logger) -> Tuple[int, int]:
    """
    Cuenta los casos encontrados desde el archivo de reporte generado.

    Args:
        logger: Logger instance

    Returns:
        Tuple of (tickets_count, incidents_count)
    """
    reporte_file = Path(__file__).parent / "reporte_unificado.json"

    tickets_count = 0
    incidentes_count = 0

    if reporte_file.exists():
        try:
            with open(reporte_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Contar tickets
            if "tickets" in data and isinstance(data["tickets"], dict):
                tickets_count = data["tickets"].get("total_tickets", 0)

            # Contar incidentes
            if "incidents" in data and isinstance(data["incidents"], dict):
                incidentes_count = data["incidents"].get("total_incidentes", 0)

            logger.debug(f"Casos contados desde {reporte_file}: {tickets_count} tickets, {incidentes_count} incidentes")

        except Exception as e:
            logger.warning(f"Error al leer el archivo de reporte {reporte_file}: {str(e)}")
    else:
        logger.warning(f"Archivo de reporte no encontrado: {reporte_file}")

    return tickets_count, incidentes_count


def main():
    """Función principal que procesa los argumentos y ejecuta el flujo"""
    parser = argparse.ArgumentParser(
        description='Buscar casos en BMC Remedy y enviar reporte por correo',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Ejemplos de uso:
  %(prog)s 1152213619
  %(prog)s 1152213619 --destinatario <EMAIL>
  %(prog)s 1152213619 --todos --silent
  %(prog)s 1152213619 --debug
        """
    )

    # Argumentos principales
    parser.add_argument('cedula', help='Número de cédula a consultar')
    parser.add_argument('--destinatario', '-d', help='Correo del destinatario (opcional)')
    parser.add_argument('--todos', '-t', action='store_true', help='Mostrar todos los incidentes')

    # Argumentos de configuración
    parser.add_argument('--silent', '-s', action='store_true', help='Modo silencioso (solo logs de error)')
    parser.add_argument('--debug', action='store_true', help='Modo debug (logs detallados)')
    parser.add_argument('--verbose', '-v', action='store_true', help='Modo verbose (logs detallados)')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], help='Nivel de logging')
    parser.add_argument('--log-output', choices=['console', 'file', 'both', 'silent'], help='Destino de logs')

    args = parser.parse_args()

    try:
        # Setup configuration from arguments
        config = setup_from_args(args)

        # Setup logging with the configuration
        setup_logging(
            app_name=config.app_name,
            log_level=config.logging.level,
            output_mode=config.logging.output_mode,
            log_dir=config.logging.log_dir,
            enable_colors=config.logging.enable_colors
        )

        # Get logger after setup
        logger = get_logger('main')

        # Log startup information
        log_operation_start(
            logger, "aplicación principal",
            cedula=args.cedula,
            destinatario=args.destinatario,
            todos=args.todos,
            version=config.version,
            debug_mode=config.debug,
            silent_mode=config.silent_mode
        )

        # Ejecutar el flujo completo
        resultado = ejecutar_flujo_completo(args.cedula, args.destinatario, args.todos)

        # Salir con código de error si hubo problemas
        if not resultado:
            logger.error("El proceso terminó con errores")
            sys.exit(1)
        else:
            log_operation_success(
                logger, "aplicación principal",
                cedula=args.cedula,
                destinatario=args.destinatario
            )

    except KeyboardInterrupt:
        logger = get_logger('main')
        logger.warning("Proceso interrumpido por el usuario")
        sys.exit(130)  # Standard exit code for Ctrl+C

    except Exception as e:
        logger = get_logger('main')
        log_operation_error(logger, "aplicación principal", e)
        sys.exit(1)


if __name__ == "__main__":
    main()
