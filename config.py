#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Configuration management for BMC Remedy Container application.
Centralizes all configuration settings and provides environment-based configuration.
"""

import os
from dataclasses import dataclass, field
from pathlib import Path
from typing import Optional, Dict, Any
from dotenv import load_dotenv


@dataclass
class LoggingConfig:
    """Configuration for logging system."""
    level: str = "INFO"
    output_mode: str = "both"  # console, file, both, silent
    log_dir: str = "logs"
    enable_colors: bool = True
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5


@dataclass
class BMCConfig:
    """Configuration for BMC Remedy API."""
    api_base_url: str = ""
    login_url: str = ""
    username: str = ""
    password: str = ""
    timeout: int = 30
    verify_ssl: bool = False


@dataclass
class EmailConfig:
    """Configuration for email system."""
    mail_url: str = ""
    default_recipient: str = ""
    timeout: int = 10
    verify_ssl: bool = False


@dataclass
class ApplicationConfig:
    """Main application configuration."""
    app_name: str = "bmc-remedy"
    version: str = "1.0.0"
    debug: bool = False
    silent_mode: bool = False
    max_incidents_display: int = 5
    
    # Sub-configurations
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    bmc: BMCConfig = field(default_factory=BMCConfig)
    email: EmailConfig = field(default_factory=EmailConfig)


class ConfigManager:
    """Manages application configuration from environment variables and files."""
    
    def __init__(self, env_file: Optional[str] = None):
        """
        Initialize configuration manager.
        
        Args:
            env_file: Path to .env file (optional)
        """
        self.env_file = env_file or ".env"
        self._config: Optional[ApplicationConfig] = None
        self._load_environment()
    
    def _load_environment(self):
        """Load environment variables from .env file if it exists."""
        env_path = Path(self.env_file)
        if env_path.exists():
            load_dotenv(env_path)
    
    def get_config(self) -> ApplicationConfig:
        """
        Get the application configuration.
        
        Returns:
            ApplicationConfig instance with all settings
        """
        if self._config is None:
            self._config = self._build_config()
        return self._config
    
    def _build_config(self) -> ApplicationConfig:
        """Build configuration from environment variables."""
        # Logging configuration
        logging_config = LoggingConfig(
            level=os.getenv("LOG_LEVEL", "INFO").upper(),
            output_mode=os.getenv("LOG_OUTPUT_MODE", "both").lower(),
            log_dir=os.getenv("LOG_DIR", "logs"),
            enable_colors=os.getenv("LOG_ENABLE_COLORS", "true").lower() == "true",
            max_file_size=int(os.getenv("LOG_MAX_FILE_SIZE", "10485760")),  # 10MB
            backup_count=int(os.getenv("LOG_BACKUP_COUNT", "5"))
        )
        
        # BMC configuration
        bmc_config = BMCConfig(
            api_base_url=os.getenv("API_BASE_URL", "https://surasoporteti-qa-restapi.onbmc.com/api"),
            login_url=os.getenv("LOGIN_URL", ""),
            username=os.getenv("USERNAME_", "Integracion.VoiceBot"),
            password=os.getenv("PASSWORD", "$ur@2025*"),
            timeout=int(os.getenv("BMC_TIMEOUT", "30")),
            verify_ssl=os.getenv("BMC_VERIFY_SSL", "false").lower() == "true"
        )
        
        # Email configuration
        email_config = EmailConfig(
            mail_url=os.getenv("MAIL_URL", "https://mosaico.arus.com.co:3000/mailer/correo/notificacion"),
            default_recipient=os.getenv("EMAIL_TO", "<EMAIL>"),
            timeout=int(os.getenv("EMAIL_TIMEOUT", "10")),
            verify_ssl=os.getenv("EMAIL_VERIFY_SSL", "false").lower() == "true"
        )
        
        # Main application configuration
        config = ApplicationConfig(
            app_name=os.getenv("APP_NAME", "bmc-remedy"),
            version=os.getenv("APP_VERSION", "1.0.0"),
            debug=os.getenv("DEBUG", "false").lower() == "true",
            silent_mode=os.getenv("SILENT_MODE", "false").lower() == "true",
            max_incidents_display=int(os.getenv("MAX_INCIDENTS_DISPLAY", "5")),
            logging=logging_config,
            bmc=bmc_config,
            email=email_config
        )
        
        return config
    
    def update_config(self, **kwargs):
        """
        Update configuration values.
        
        Args:
            **kwargs: Configuration values to update
        """
        if self._config is None:
            self._config = self._build_config()
        
        for key, value in kwargs.items():
            if hasattr(self._config, key):
                setattr(self._config, key, value)
    
    def get_env_var(self, key: str, default: Any = None) -> Any:
        """
        Get environment variable value.
        
        Args:
            key: Environment variable name
            default: Default value if not found
            
        Returns:
            Environment variable value or default
        """
        return os.getenv(key, default)
    
    def set_env_var(self, key: str, value: str):
        """
        Set environment variable.
        
        Args:
            key: Environment variable name
            value: Value to set
        """
        os.environ[key] = value
    
    def is_debug_mode(self) -> bool:
        """Check if debug mode is enabled."""
        return self.get_config().debug
    
    def is_silent_mode(self) -> bool:
        """Check if silent mode is enabled."""
        return self.get_config().silent_mode
    
    def get_log_level(self) -> str:
        """Get configured log level."""
        return self.get_config().logging.level
    
    def get_output_mode(self) -> str:
        """Get configured output mode."""
        if self.is_silent_mode():
            return "silent"
        return self.get_config().logging.output_mode


# Global configuration manager instance
_config_manager: Optional[ConfigManager] = None


def get_config_manager(env_file: Optional[str] = None) -> ConfigManager:
    """
    Get the global configuration manager instance.
    
    Args:
        env_file: Path to .env file (optional)
        
    Returns:
        ConfigManager instance
    """
    global _config_manager
    
    if _config_manager is None:
        _config_manager = ConfigManager(env_file)
    
    return _config_manager


def get_config() -> ApplicationConfig:
    """
    Get the application configuration.
    
    Returns:
        ApplicationConfig instance
    """
    return get_config_manager().get_config()


def is_debug_mode() -> bool:
    """Check if debug mode is enabled."""
    return get_config_manager().is_debug_mode()


def is_silent_mode() -> bool:
    """Check if silent mode is enabled."""
    return get_config_manager().is_silent_mode()


def setup_from_args(args) -> ApplicationConfig:
    """
    Setup configuration from command line arguments.
    
    Args:
        args: Parsed command line arguments
        
    Returns:
        Updated ApplicationConfig instance
    """
    config_manager = get_config_manager()
    
    # Update configuration based on command line arguments
    updates = {}
    
    if hasattr(args, 'debug') and args.debug:
        updates['debug'] = True
        config_manager.set_env_var('DEBUG', 'true')
        config_manager.set_env_var('LOG_LEVEL', 'DEBUG')
    
    if hasattr(args, 'silent') and args.silent:
        updates['silent_mode'] = True
        config_manager.set_env_var('SILENT_MODE', 'true')
    
    if hasattr(args, 'verbose') and args.verbose:
        config_manager.set_env_var('LOG_LEVEL', 'DEBUG')
    
    if hasattr(args, 'log_level') and args.log_level:
        config_manager.set_env_var('LOG_LEVEL', args.log_level.upper())
    
    if updates:
        config_manager.update_config(**updates)
    
    return config_manager.get_config()
