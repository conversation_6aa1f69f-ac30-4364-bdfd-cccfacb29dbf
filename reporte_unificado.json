{"user_info": {"cedula": "1143936436", "found": true, "first_name": "<PERSON><PERSON>", "last_name": "Valencia Valencia", "full_name": "<PERSON><PERSON>", "email": "<EMAIL>", "company": "SURA", "organization": "SEGUROS DE VIDA SURAMERICANA S.A."}, "tickets": {"cedula": "1143936436", "fecha_consulta": "2025-06-26 13:07:32", "total_tickets": 0, "tickets_procesados": 0, "tickets": []}, "incidents": {"login_id": "1143936436", "fecha_consulta": "2025-06-26 13:07:32", "total_incidentes": 12, "incidentes_procesados": 12, "incidentes": [{"incident_id": "INC000000008763", "summary": "Clave de red", "status": "Closed", "detailed_description": "Sin resumen disponible", "assigned_to": null, "assignee_groups": "1000000124;'lizegato';", "last_modified_date": "2025-03-10T07:01:29.000+0000", "create_date": null, "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Buenos días\n\nSe le indica al usuario que se debe comunicar el líder de TP para poder solicitar un restablecimiento de contraseña", "resolution_method": null, "dwp_number": "8796", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nBuenos días\n\nSe le indica al usuario que se debe comunicar el líder de TP para poder solicitar un restablecimiento de contraseña\n\nCategoría de resolución:\nCAPACITACION.CAPACITACION.APLICATIVOS", "last_modified_date": "2025-03-04T13:23:42.000+0000"}}, {"incident_id": "INC000000009105", "summary": "Plantilla General", "status": "Closed", "detailed_description": "Sin resumen disponible", "assigned_to": null, "assignee_groups": "1000000124;1000000155;'lizegato';", "last_modified_date": "2025-03-11T07:00:25.000+0000", "create_date": null, "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Buen día\n \n*Se comunica la usuaria a la línea de Bloqueos\n*Se valida los datos del caso\n*Se valida el estado de la cuenta desde el DA y aparece la cuenta Expirada\n*Se válida con la compañera del área de accesos K.C\n*Se hace la prueba con la usuaria y funciona\n*Usuaria queda operativa\n*Se procede con el cierre del caso\n \nSaludos", "resolution_method": null, "dwp_number": "9279", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nBuen día\n \n*Se comunica la usuaria a la línea de Bloqueos\n*Se valida los datos del caso\n*Se valida el estado de la cuenta desde el DA y aparece la cuenta Expirada\n*Se válida con la compañera del área de accesos K.C\n*Se hace la prueba con la usuaria y funciona\n*Usuaria queda operativa\n*Se procede con el cierre del caso\n \nSaludos\n\nCategoría de resolución:\nGESTION DE CUENTAS DE USUARIO.ACTIVAR.USUARIO", "last_modified_date": "2025-03-05T20:47:14.000+0000"}}, {"incident_id": "INC000000012374", "summary": "Autenticador", "status": "Closed", "detailed_description": "Sin resumen disponible", "assigned_to": null, "assignee_groups": "1000000124;1000000135;'lizeusbo';", "last_modified_date": "2025-03-17T07:01:45.000+0000", "create_date": null, "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Cordial saludo,\n \n* Usuario se comunica a la línea de MFA.\n* Se restablece configuración MFA desde el Equipo SOC.\n* Se confirma operatividad por parte del agente de mesa que atendió la llamada.\n* Usuario autoriza cierre del incidente.\n* Se procede al cierre del incidente.\n \nMuchas gracias", "resolution_method": null, "dwp_number": "14710", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nCordial saludo,\n \n* Usuario se comunica a la línea de MFA.\n* Se restablece configuración MFA desde el Equipo SOC.\n* Se confirma operatividad por parte del agente de mesa que atendió la llamada.\n* Usuario autoriza cierre del incidente.\n* Se procede al cierre del incidente.\n \nMuchas gracias\n\nCategoría de resolución:\nGESTION DE CUENTAS DE USUARIO.REESTABLECIMIENTO.MFA", "last_modified_date": "2025-03-11T14:30:18.000+0000"}}, {"incident_id": "INC000000013114", "summary": "No le aparece la orden en SAP para facturar.", "status": "Closed", "detailed_description": "Sin resumen disponible", "assigned_to": null, "assignee_groups": "1000000134;1000000400;'lizecosl';", "last_modified_date": "2025-03-20T07:00:28.000+0000", "create_date": null, "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Buen día, se reprocesa orden 932-1307456100 y viaja a sap con número de autorización 9031995739", "resolution_method": null, "dwp_number": "15698", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nBuen día, se reprocesa orden 932-1307456100 y viaja a sap con número de autorización 9031995739\n\nCategoría de resolución:\nMODIFICACIONES.MODIFICAR.DATO DEFINICION DE NEGOCIO", "last_modified_date": "2025-03-14T19:15:31.000+0000"}}, {"incident_id": "INC000000014189", "summary": "Otras Aps Empresarial", "status": "Closed", "detailed_description": "Sin resumen disponible", "assigned_to": null, "assignee_groups": "1000000125;1000000397;'lizevava';", "last_modified_date": "2025-03-20T07:01:14.000+0000", "create_date": null, "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): proceso del area financiera de la compañia\nSolución: Se realiza contacto con la usuaria y Juan guillermo ruiz duque del area financiera para procesos de desembolsos para que le informe a la usuaria directamente porque no ha salido la devolución para el cliente, Por este canal no es posible indicarle porque no ha salido el pago dado que no es manejado por nosotros.\nCausa Raíz (Identificada/Sin identificar):Sin identificar\nConfirmar operatividad del usuario Afectado (SI/NO): NO", "resolution_method": null, "dwp_number": "17360", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nCausa (Motivo, origen o razón de que suceda el incidente): proceso del area financiera de la compañia\nSolución: Se realiza contacto con la usuaria y Juan guillermo ruiz duque del area financiera para procesos de desembolsos para que le informe a la usuaria directamente porque no ha salido la devolución para el cliente, Por este canal no es posible indicarle porque no ha salido el pago dado que no es manejado por nosotros.\nCausa Raíz (Identificada/Sin identificar):Sin identificar\nConfirmar operatividad del usuario Afectado (SI/NO): NO\n\nCategoría de resolución:\nCAPACITACION.CAPACITACION.PROCESO DE NEGOCIO", "last_modified_date": "2025-03-14T19:55:58.000+0000"}}, {"incident_id": "INC000000016058", "summary": "Sitios Web", "status": "Closed", "detailed_description": "Sin resumen disponible", "assigned_to": null, "assignee_groups": "1000000518;1000000387;'lizevava';", "last_modified_date": "2025-05-06T03:52:45.000+0000", "create_date": null, "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Se actualizo version con ajuste en modulo de biometricos que resolvio la falla\nSe pregunto al usuario y esta operativo", "resolution_method": null, "dwp_number": "19867", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Se adjunta apribacion del cliente", "last_modified_date": "2025-04-02T14:13:09.000+0000"}}, {"incident_id": "INC000000016498", "summary": "Sitios Web", "status": "Cancelled", "detailed_description": "Sin resumen disponible", "assigned_to": null, "assignee_groups": "1000000518;'lizevava';", "last_modified_date": "2025-06-03T02:20:50.000+0000", "create_date": null, "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "20778", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Buen día\n\nDesde la SVP y validación con Lizeth quien levanto el incidente, no se ha tenido novedad con el usuario que reporta el tema de poliza hogar.\n\nMuchas Gracias", "last_modified_date": "2025-04-24T13:47:47.000+0000"}}, {"incident_id": "INC000000017209", "summary": "restablecer mfa", "status": "Closed", "detailed_description": "Sin resumen disponible", "assigned_to": null, "assignee_groups": "1000000124;'lizegato';", "last_modified_date": "2025-03-26T07:01:36.000+0000", "create_date": null, "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Buen día\n\n*Se comunica el usuario a la línea de MFA\n*Se valida los datos del usuario\n*Se solicita restablecimiento del MFA\n*Se hace pruebas y funciona\n*Usuario autoriza el cierre del caso\n\nFeliz día", "resolution_method": null, "dwp_number": "21595", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nBuen día\n\n*Se comunica el usuario a la línea de MFA\n*Se valida los datos del usuario\n*Se solicita restablecimiento del MFA\n*Se hace pruebas y funciona\n*Usuario autoriza el cierre del caso\n\nFeliz día\n\nCategoría de resolución:\nGESTION DE CUENTAS DE USUARIO.REESTABLECIMIENTO.MFA", "last_modified_date": "2025-03-20T11:11:24.000+0000"}}, {"incident_id": "INC000000051808", "summary": "Otras Aps Empresarial", "status": "Assigned", "detailed_description": "Sin resumen disponible", "assigned_to": null, "assignee_groups": "1000000125;1000000319;'lizevava';", "last_modified_date": "2025-06-09T13:23:54.000+0000", "create_date": null, "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "65883", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "se reviso en conjunto con <PERSON> y con <PERSON> y se evidenció que no es un tema de parametrización, lo que pasa es que cuando el usuario tiene 2 pólizas ( en este caso los usuarios reportados tienen póliza juvenil y Evoluciona), está generando un error porque está intentando asignar la cita por la póliza juvenil. \nEste ajuste requiere un raizal, se va a dar prioridad y la idea es salir a producción el día lunes.\nPara estos 3 casos se va a intentar suprimirlos por base de datos y que devuelva primero la póliza evoluciona y no la juvenil.", "last_modified_date": "2025-06-04T16:11:31.000+0000"}}, {"incident_id": "INC000000051852", "summary": "Otras Aps Empresarial", "status": "Assigned", "detailed_description": "Sin resumen disponible", "assigned_to": null, "assignee_groups": "1000000125;1000000319;'lizevava';", "last_modified_date": "2025-06-09T13:24:21.000+0000", "create_date": null, "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "65957", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "se reviso en conjunto con <PERSON> y con <PERSON> y se evidenció que no es un tema de parametrización, lo que pasa es que cuando el usuario tiene 2 pólizas ( en este caso los usuarios reportados tienen póliza juvenil y Evoluciona), está generando un error porque está intentando asignar la cita por la póliza juvenil. \nEste ajuste requiere un raizal, se va a dar prioridad y la idea es salir a producción el día lunes.\nPara estos 3 casos se va a intentar suprimirlos por base de datos y que devuelva primero la póliza evoluciona y no la juvenil.", "last_modified_date": "2025-06-04T16:12:21.000+0000"}}, {"incident_id": "INC000000051737", "summary": "Otras Aps Empresarial", "status": "Closed", "detailed_description": "Sin resumen disponible", "assigned_to": null, "assignee_groups": "1000000125;1000000387;'lizevava';", "last_modified_date": "2025-06-02T07:03:24.000+0000", "create_date": null, "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): La niña Laurent Ballesteros aparece en el grupo familiar del cliente con documento\": \"11809403\", \"CARLOS ALBERTO\" \"BALLESTEROS GUTIERREZ\", y por ser el el titular de la poliza especial 090001292948 es quien puede agendarle citas medicas a la niña.  Necesitamos saber que persona necesita agendarle citas porque en la descripción de la falla no aparece documento y ademas cuando dice que la pagina en linea, si le permite se refiere a la sucursal virtual personas Sura o a la de EPS?\nSolución: Capacitacion aplicativos\nCausa Raíz (Identificada/Sin identificar):identificada\nConfirmar operatividad del usuario Afectado (SI/NO):", "resolution_method": null, "dwp_number": "65778", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "La niña Laurent Ballesteros aparece en el grupo familiar del cliente con documento\": \"11809403\", \"CARLOS ALBERTO\" \"BALLESTEROS GUTIERREZ\", y por ser el el titular de la poliza especial 090001292948 es quien puede agendarle citas medicas a la niña.  Necesitamos saber que persona necesita agendarle citas porque en la descripción de la falla no aparece documento y ademas cuando dice que la pagina en linea, si le permite se refiere a la sucursal virtual personas Sura o a la de EPS?", "last_modified_date": "2025-05-27T19:42:39.000+0000"}}, {"incident_id": "INC000000052762", "summary": "Otras Aps Empresarial", "status": "Assigned", "detailed_description": "Sin resumen disponible", "assigned_to": null, "assignee_groups": "1000000125;1000000379;'lizevava';", "last_modified_date": "2025-06-09T21:30:16.000+0000", "create_date": null, "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "67157", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Buena tarde,\n\n*Se válida los datos\n*Se valida imágenes anexas\n*Se escala caso al único analista del grupo\n*Se escala caso para su amable gestión\n\nFeliz día", "last_modified_date": "2025-05-29T00:58:51.000+0000"}}]}}