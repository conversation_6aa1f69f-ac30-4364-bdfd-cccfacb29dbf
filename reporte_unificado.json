{"user_info": {"cedula": "65883", "found": false, "first_name": "", "last_name": "", "full_name": "", "email": "", "company": "", "organization": ""}, "tickets": {"cedula": "65883", "fecha_consulta": "2025-06-26 13:13:59", "total_tickets": 0, "tickets_procesados": 0, "tickets": []}, "incidents": {"login_id": "65883", "fecha_consulta": "2025-06-26 13:13:59", "total_incidentes": 1, "incidentes_procesados": 1, "incidentes": [{"incident_id": "INC000000051808", "summary": "Otras Aps Empresarial", "status": "Assigned", "detailed_description": "Sin resumen disponible", "assigned_to": null, "assignee_groups": "1000000125;1000000319;'lizevava';", "last_modified_date": "2025-06-09T13:23:54.000+0000", "create_date": null, "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "65883", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "se reviso en conjunto con <PERSON> y con <PERSON> y se evidenció que no es un tema de parametrización, lo que pasa es que cuando el usuario tiene 2 pólizas ( en este caso los usuarios reportados tienen póliza juvenil y Evoluciona), está generando un error porque está intentando asignar la cita por la póliza juvenil. \nEste ajuste requiere un raizal, se va a dar prioridad y la idea es salir a producción el día lunes.\nPara estos 3 casos se va a intentar suprimirlos por base de datos y que devuelva primero la póliza evoluciona y no la juvenil.", "last_modified_date": "2025-06-04T16:11:31.000+0000"}}]}}