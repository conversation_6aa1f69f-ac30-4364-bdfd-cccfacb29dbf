{"user_info": {"cedula": "1143936436", "found": true, "first_name": "<PERSON><PERSON>", "last_name": "Valencia Valencia", "full_name": "<PERSON><PERSON>", "email": "<EMAIL>", "company": "SURA", "organization": "SEGUROS DE VIDA SURAMERICANA S.A."}, "tickets": {"cedula": "1143936436", "fecha_consulta": "2025-06-26 13:20:13", "total_tickets": 0, "tickets_procesados": 0, "tickets": []}, "incidents": {"login_id": "1143936436", "fecha_consulta": "2025-06-26 13:20:13", "total_incidentes": 12, "incidentes_procesados": 12, "incidentes": [{"incident_id": "INC000000008763", "summary": "Clave de red", "status": "Closed", "detailed_description": "Descripción: Clave de red\\n\\nInformación de Aplicativos/Equipos:\\nz1D_CI_FormName: AST:LAN\\nHPD_CI_ReconID: OI-784AF224533211EFB06C1AF001D8BC3E\\nHPD_CI_FormName: BMC_LAN\\nAssociated Alarm: None\\nDirect Contact City: MEDELLIN\\nResolution Category Tier 3: APLICATIVOS\\nClosure Product Category Tier1: Somos Sura\\nResolution Category Tier 2: CAPACITACION\\nClosure Product Category Tier2: Tranversal\\nHPD_CI: RED MICROSOFT\\nClosure Product Category Tier3: Tranversal\\nDWP_SRID: 8796\\nIncident Number: INC000000008763\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Contrasena Red Microsoft\\nCategorization Tier 3: No puede ingresar\\nProduct Categorization Tier 1: Somos <PERSON>a\\nProduct Categorization Tier 3: Tranversal\\nProduct Categorization Tier 2: Tranversal\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000124;'lizegato';", "last_modified_date": "2025-03-10T07:01:29.000+0000", "create_date": null, "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Buenos días\n\nSe le indica al usuario que se debe comunicar el líder de TP para poder solicitar un restablecimiento de contraseña", "resolution_method": null, "dwp_number": "8796", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nBuenos días\n\nSe le indica al usuario que se debe comunicar el líder de TP para poder solicitar un restablecimiento de contraseña\n\nCategoría de resolución:\nCAPACITACION.CAPACITACION.APLICATIVOS", "last_modified_date": "2025-03-04T13:23:42.000+0000"}}, {"incident_id": "INC000000009105", "summary": "Plantilla General", "status": "Closed", "detailed_description": "Descripción: Plantilla General\\n\\nInformación de Aplicativos/Equipos:\\nz1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-026B5B10892011EF89355E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nDirect Contact City: MEDELLIN\\nResolution Category Tier 3: USUARIO\\nClosure Product Category Tier1: Directorio Activo\\nResolution Category Tier 2: ACTIVAR\\nClosure Product Category Tier2: Directorio Activo\\nHPD_CI: SomosSura\\nClosure Product Category Tier3: Directorio Activo\\nDWP_SRID: 9279\\nIncident Number: INC000000009105\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: APLICACIONES SUR\\nCategorization Tier 2: Somos Sura\\nCategorization Tier 3: No puede ingresar\\nProduct Categorization Tier 1: Aplicaciones Transversales\\nProduct Categorization Tier 3: Somos Sura\\nProduct Categorization Tier 2: Somos Sura\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000124;1000000155;'lizegato';", "last_modified_date": "2025-03-11T07:00:25.000+0000", "create_date": null, "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Buen día\n \n*Se comunica la usuaria a la línea de Bloqueos\n*Se valida los datos del caso\n*Se valida el estado de la cuenta desde el DA y aparece la cuenta Expirada\n*Se válida con la compañera del área de accesos K.C\n*Se hace la prueba con la usuaria y funciona\n*Usuaria queda operativa\n*Se procede con el cierre del caso\n \nSaludos", "resolution_method": null, "dwp_number": "9279", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nBuen día\n \n*Se comunica la usuaria a la línea de Bloqueos\n*Se valida los datos del caso\n*Se valida el estado de la cuenta desde el DA y aparece la cuenta Expirada\n*Se válida con la compañera del área de accesos K.C\n*Se hace la prueba con la usuaria y funciona\n*Usuaria queda operativa\n*Se procede con el cierre del caso\n \nSaludos\n\nCategoría de resolución:\nGESTION DE CUENTAS DE USUARIO.ACTIVAR.USUARIO", "last_modified_date": "2025-03-05T20:47:14.000+0000"}}, {"incident_id": "INC000000012374", "summary": "Autenticador", "status": "Closed", "detailed_description": "Descripción: Autenticador\\n\\nInformación de Aplicativos/Equipos:\\nz1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-02DCD538892011EF899B5E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nDirect Contact City: MEDELLIN\\nResolution Category Tier 3: MFA\\nClosure Product Category Tier1: Somos Sura\\nResolution Category Tier 2: REESTABLECIMIENTO\\nClosure Product Category Tier2: Tranversal\\nHPD_CI: Seus 4\\nClosure Product Category Tier3: Tranversal\\nDWP_SRID: 14710\\nIncident Number: INC000000012374\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: APLICACIONES SUR\\nCategorization Tier 2: Somos Sura\\nCategorization Tier 3: No puede ingresar\\nProduct Categorization Tier 1: Somos Sura\\nProduct Categorization Tier 3: Tranversal\\nProduct Categorization Tier 2: Tranversal\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000124;1000000135;'lizeusbo';", "last_modified_date": "2025-03-17T07:01:45.000+0000", "create_date": null, "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Cordial saludo,\n \n* Usuario se comunica a la línea de MFA.\n* Se restablece configuración MFA desde el Equipo SOC.\n* Se confirma operatividad por parte del agente de mesa que atendió la llamada.\n* Usuario autoriza cierre del incidente.\n* Se procede al cierre del incidente.\n \nMuchas gracias", "resolution_method": null, "dwp_number": "14710", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nCordial saludo,\n \n* Usuario se comunica a la línea de MFA.\n* Se restablece configuración MFA desde el Equipo SOC.\n* Se confirma operatividad por parte del agente de mesa que atendió la llamada.\n* Usuario autoriza cierre del incidente.\n* Se procede al cierre del incidente.\n \nMuchas gracias\n\nCategoría de resolución:\nGESTION DE CUENTAS DE USUARIO.REESTABLECIMIENTO.MFA", "last_modified_date": "2025-03-11T14:30:18.000+0000"}}, {"incident_id": "INC000000013114", "summary": "No le aparece la orden en SAP para facturar.", "status": "Closed", "detailed_description": "Descripción: No le aparece la orden en SAP para facturar.\\n\\nInformación de Aplicativos/Equipos:\\nz1D_CI_FormName: AST:CDROMDrive\\nHPD_CI_ReconID: OI-14F86F50EC2311EFBA193ACEF3832554\\nHPD_CI_FormName: BMC_CDROMDRIVE\\nAssociated Alarm: None\\nDirect Contact City: BELLO\\nResolution Category Tier 3: DATO DEFINICION DE NEGOCIO\\nClosure Product Category Tier1: SAP\\nResolution Category Tier 2: MODIFICAR\\nClosure Product Category Tier2: OPC  -  IPS\\nHPD_CI: Old_SAP-OPC-IPS\\nClosure Product Category Tier3: Navegacion y Configuracion\\nDWP_SRID: 15698\\nIncident Number: INC000000013114\\nRequestCreatedFromDWP: No\\nCity: BELLO\\nCategorization Tier 1: SUR\\nCategorization Tier 2: SAP\\nCategorization Tier 3: Falla\\nProduct Categorization Tier 1: SAP\\nProduct Categorization Tier 3: Navegacion y Configuracion\\nProduct Categorization Tier 2: OPC  -  IPS\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000134;1000000400;'lizecosl';", "last_modified_date": "2025-03-20T07:00:28.000+0000", "create_date": null, "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Buen día, se reprocesa orden 932-1307456100 y viaja a sap con número de autorización 9031995739", "resolution_method": null, "dwp_number": "15698", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nBuen día, se reprocesa orden 932-1307456100 y viaja a sap con número de autorización 9031995739\n\nCategoría de resolución:\nMODIFICACIONES.MODIFICAR.DATO DEFINICION DE NEGOCIO", "last_modified_date": "2025-03-14T19:15:31.000+0000"}}, {"incident_id": "INC000000014189", "summary": "Otras Aps Empresarial", "status": "Closed", "detailed_description": "Descripción: Otras Aps Empresarial\\n\\nInformación de Aplicativos/Equipos:\\nz1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-03619854892011EF8A165E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: PROCESO DE NEGOCIO\\nClosure Product Category Tier1: Bancaseguros\\nResolution Category Tier 2: CAPACITACION\\nClosure Product Category Tier2: Canal Banca\\nHPD_CI: SAP-AR\\nClosure Product Category Tier3: Canal Banca\\nDWP_SRID: 17360\\nDWP_SRInstanceID: AGGJYA2JFNGSXASTCTP2STCTP2E14J\\nIncident Number: INC000000014189\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SAP\\nCategorization Tier 2: FI - AR Cuentas por cobrar\\nCategorization Tier 3: Navegacion y Configuracion\\nProduct Categorization Tier 1: Bancaseguros\\nProduct Categorization Tier 3: Canal Banca\\nProduct Categorization Tier 2: Canal Banca\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000125;1000000397;'lizevava';", "last_modified_date": "2025-03-20T07:01:14.000+0000", "create_date": null, "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): proceso del area financiera de la compañia\nSolución: Se realiza contacto con la usuaria y Juan guillermo ruiz duque del area financiera para procesos de desembolsos para que le informe a la usuaria directamente porque no ha salido la devolución para el cliente, Por este canal no es posible indicarle porque no ha salido el pago dado que no es manejado por nosotros.\nCausa Raíz (Identificada/Sin identificar):Sin identificar\nConfirmar operatividad del usuario Afectado (SI/NO): NO", "resolution_method": null, "dwp_number": "17360", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nCausa (Motivo, origen o razón de que suceda el incidente): proceso del area financiera de la compañia\nSolución: Se realiza contacto con la usuaria y Juan guillermo ruiz duque del area financiera para procesos de desembolsos para que le informe a la usuaria directamente porque no ha salido la devolución para el cliente, Por este canal no es posible indicarle porque no ha salido el pago dado que no es manejado por nosotros.\nCausa Raíz (Identificada/Sin identificar):Sin identificar\nConfirmar operatividad del usuario Afectado (SI/NO): NO\n\nCategoría de resolución:\nCAPACITACION.CAPACITACION.PROCESO DE NEGOCIO", "last_modified_date": "2025-03-14T19:55:58.000+0000"}}, {"incident_id": "INC000000016058", "summary": "Sitios Web", "status": "Closed", "detailed_description": "Descripción: Sitios Web\\n\\nInformación de Aplicativos/Equipos:\\nz1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-02161B96892011EF88EA5E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: VERSION\\nClosure Product Category Tier1: SuperAPP\\nResolution Category Tier 2: ACTUALIZAR\\nClosure Product Category Tier2: SuperAPP\\nHPD_CI: SuperAPP\\nClosure Product Category Tier3: SuperAPP\\nDWP_SRID: 19867\\nDWP_SRInstanceID: AGGEFKK2DFA3IAST1NNUST1NNULJ7N\\nIncident Number: INC000000016058\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: SuperAPP\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: SuperAPP\\nProduct Categorization Tier 3: SuperAPP\\nProduct Categorization Tier 2: SuperAPP\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000518;1000000387;'lizevava';", "last_modified_date": "2025-05-06T03:52:45.000+0000", "create_date": null, "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Se actualizo version con ajuste en modulo de biometricos que resolvio la falla\nSe pregunto al usuario y esta operativo", "resolution_method": null, "dwp_number": "19867", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Se adjunta apribacion del cliente", "last_modified_date": "2025-04-02T14:13:09.000+0000"}}, {"incident_id": "INC000000016498", "summary": "Sitios Web", "status": "Cancelled", "detailed_description": "Descripción: Sitios Web\\n\\nInformación de Aplicativos/Equipos:\\nz1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-01D93348892011EF88AE5E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: NO EXITOSO\\nClosure Product Category Tier1: Sucursal Virtual Personas\\nResolution Category Tier 2: CONTACTO\\nClosure Product Category Tier2: Sucursal Virtual Personas\\nHPD_CI: PortalesAPI\\nClosure Product Category Tier3: Sucursal Virtual Personas\\nDWP_SRID: 20778\\nDWP_SRInstanceID: AGGEFKK2DFA3IAST2O4VST2O4VN2WK\\nIncident Number: INC000000016498\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Sucursal Virtual Personas\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: Sucursal Virtual Empresas\\nProduct Categorization Tier 3: Sucursal Virtual Empresas\\nProduct Categorization Tier 2: Sucursal Virtual Empresas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000518;'lizevava';", "last_modified_date": "2025-06-03T02:20:50.000+0000", "create_date": null, "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "20778", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Buen día\n\nDesde la SVP y validación con Lizeth quien levanto el incidente, no se ha tenido novedad con el usuario que reporta el tema de poliza hogar.\n\nMuchas Gracias", "last_modified_date": "2025-04-24T13:47:47.000+0000"}}, {"incident_id": "INC000000017209", "summary": "restablecer mfa", "status": "Closed", "detailed_description": "Descripción: restablecer mfa\\n\\nInformación de Aplicativos/Equipos:\\nz1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-01BF46CC892011EF889B5E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nDirect Contact City: MEDELLIN\\nResolution Category Tier 3: MFA\\nClosure Product Category Tier1: Saludweb\\nResolution Category Tier 2: REESTABLECIMIENTO\\nClosure Product Category Tier2: Administracion Solucion\\nHPD_CI: Saludweb\\nClosure Product Category Tier3: Administracion Solucion\\nDWP_SRID: 21595\\nIncident Number: INC000000017209\\nRequestCreatedFromDWP: No\\nCity: MEDELLIN\\nCategorization Tier 1: EPS\\nCategorization Tier 2: Saludweb\\nCategorization Tier 3: No puede ingresar\\nProduct Categorization Tier 1: Saludweb\\nProduct Categorization Tier 3: Administracion Solucion\\nProduct Categorization Tier 2: Administracion Solucion\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000124;'lizegato';", "last_modified_date": "2025-03-26T07:01:36.000+0000", "create_date": null, "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Buen día\n\n*Se comunica el usuario a la línea de MFA\n*Se valida los datos del usuario\n*Se solicita restablecimiento del MFA\n*Se hace pruebas y funciona\n*Usuario autoriza el cierre del caso\n\nFeliz día", "resolution_method": null, "dwp_number": "21595", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nBuen día\n\n*Se comunica el usuario a la línea de MFA\n*Se valida los datos del usuario\n*Se solicita restablecimiento del MFA\n*Se hace pruebas y funciona\n*Usuario autoriza el cierre del caso\n\nFeliz día\n\nCategoría de resolución:\nGESTION DE CUENTAS DE USUARIO.REESTABLECIMIENTO.MFA", "last_modified_date": "2025-03-20T11:11:24.000+0000"}}, {"incident_id": "INC000000051808", "summary": "Otras Aps Empresarial", "status": "Assigned", "detailed_description": "Descripción: Otras Aps Empresarial\\n\\nInformación de Aplicativos/Equipos:\\nz1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-02161B96892011EF88EA5E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nClosure Product Category Tier1: Aplicaciones\\nClosure Product Category Tier2: Aplicaciones de negocio\\nHPD_CI: SuperAPP\\nClosure Product Category Tier3: Aplicaciones de negocio\\nDWP_SRID: 65883\\nDWP_SRInstanceID: AGGJYG8IF3BYEASWX9TYSWX9TYBO3O\\nIncident Number: INC000000051808\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: SuperAPP\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: SuperAPP\\nProduct Categorization Tier 3: SuperAPP\\nProduct Categorization Tier 2: SuperAPP\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000125;1000000319;'lizevava';", "last_modified_date": "2025-06-09T13:23:54.000+0000", "create_date": null, "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "65883", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "se reviso en conjunto con <PERSON> y con <PERSON> y se evidenció que no es un tema de parametrización, lo que pasa es que cuando el usuario tiene 2 pólizas ( en este caso los usuarios reportados tienen póliza juvenil y Evoluciona), está generando un error porque está intentando asignar la cita por la póliza juvenil. \nEste ajuste requiere un raizal, se va a dar prioridad y la idea es salir a producción el día lunes.\nPara estos 3 casos se va a intentar suprimirlos por base de datos y que devuelva primero la póliza evoluciona y no la juvenil.", "last_modified_date": "2025-06-04T16:11:31.000+0000"}}, {"incident_id": "INC000000051852", "summary": "Otras Aps Empresarial", "status": "Assigned", "detailed_description": "Descripción: Otras Aps Empresarial\\n\\nInformación de Aplicativos/Equipos:\\nz1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-02161B96892011EF88EA5E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nHPD_CI: SuperAPP\\nDWP_SRID: 65957\\nDWP_SRInstanceID: AGGJYG8IF3BYEASWXPYDSWXPYDGYPP\\nIncident Number: INC000000051852\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: SuperAPP\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: SuperAPP\\nProduct Categorization Tier 3: SuperAPP\\nProduct Categorization Tier 2: SuperAPP\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000125;1000000319;'lizevava';", "last_modified_date": "2025-06-09T13:24:21.000+0000", "create_date": null, "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "65957", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "se reviso en conjunto con <PERSON> y con <PERSON> y se evidenció que no es un tema de parametrización, lo que pasa es que cuando el usuario tiene 2 pólizas ( en este caso los usuarios reportados tienen póliza juvenil y Evoluciona), está generando un error porque está intentando asignar la cita por la póliza juvenil. \nEste ajuste requiere un raizal, se va a dar prioridad y la idea es salir a producción el día lunes.\nPara estos 3 casos se va a intentar suprimirlos por base de datos y que devuelva primero la póliza evoluciona y no la juvenil.", "last_modified_date": "2025-06-04T16:12:21.000+0000"}}, {"incident_id": "INC000000051737", "summary": "Otras Aps Empresarial", "status": "Closed", "detailed_description": "Descripción: Otras Aps Empresarial\\n\\nInformación de Aplicativos/Equipos:\\nz1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-02161B96892011EF88EA5E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: APLICATIVOS\\nClosure Product Category Tier1: SuperAPP\\nResolution Category Tier 2: CAPACITACION\\nClosure Product Category Tier2: SuperAPP\\nHPD_CI: SuperAPP\\nClosure Product Category Tier3: SuperAPP\\nDWP_SRID: 65778\\nDWP_SRInstanceID: AGGHE4WHFZM13ASWXKEXSWXKEXPTD4\\nIncident Number: INC000000051737\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: SuperAPP\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: SuperAPP\\nProduct Categorization Tier 3: SuperAPP\\nProduct Categorization Tier 2: SuperAPP\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000125;1000000387;'lizevava';", "last_modified_date": "2025-06-02T07:03:24.000+0000", "create_date": null, "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): La niña Laurent Ballesteros aparece en el grupo familiar del cliente con documento\": \"11809403\", \"CARLOS ALBERTO\" \"BALLESTEROS GUTIERREZ\", y por ser el el titular de la poliza especial 090001292948 es quien puede agendarle citas medicas a la niña.  Necesitamos saber que persona necesita agendarle citas porque en la descripción de la falla no aparece documento y ademas cuando dice que la pagina en linea, si le permite se refiere a la sucursal virtual personas Sura o a la de EPS?\nSolución: Capacitacion aplicativos\nCausa Raíz (Identificada/Sin identificar):identificada\nConfirmar operatividad del usuario Afectado (SI/NO):", "resolution_method": null, "dwp_number": "65778", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "La niña Laurent Ballesteros aparece en el grupo familiar del cliente con documento\": \"11809403\", \"CARLOS ALBERTO\" \"BALLESTEROS GUTIERREZ\", y por ser el el titular de la poliza especial 090001292948 es quien puede agendarle citas medicas a la niña.  Necesitamos saber que persona necesita agendarle citas porque en la descripción de la falla no aparece documento y ademas cuando dice que la pagina en linea, si le permite se refiere a la sucursal virtual personas Sura o a la de EPS?", "last_modified_date": "2025-05-27T19:42:39.000+0000"}}, {"incident_id": "INC000000052762", "summary": "Otras Aps Empresarial", "status": "Assigned", "detailed_description": "Descripción: Otras Aps Empresarial\\n\\nInformación de Aplicativos/Equipos:\\nz1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-0374265E892011EF8A285E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nClosure Product Category Tier1: Aplicaciones\\nClosure Product Category Tier2: Aplicaciones de negocio\\nHPD_CI: CCM\\nClosure Product Category Tier3: Aplicaciones de negocio\\nDWP_SRID: 67157\\nDWP_SRInstanceID: AGGJYG8IF3BYEASWZYO5SWZYO5DP8G\\nIncident Number: INC000000052762\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: CCM\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: CCM\\nProduct Categorization Tier 3: Gestión de comunicaciones\\nProduct Categorization Tier 2: Transversales\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000125;1000000379;'lizevava';", "last_modified_date": "2025-06-09T21:30:16.000+0000", "create_date": null, "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "67157", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Buena tarde,\n\n*Se válida los datos\n*Se valida imágenes anexas\n*Se escala caso al único analista del grupo\n*Se escala caso para su amable gestión\n\nFeliz día", "last_modified_date": "2025-05-29T00:58:51.000+0000"}}]}}