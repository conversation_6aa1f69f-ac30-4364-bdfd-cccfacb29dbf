{"user_info": {"cedula": "1143936436", "found": true, "first_name": "<PERSON><PERSON>", "last_name": "Valencia Valencia", "full_name": "<PERSON><PERSON>", "email": "<EMAIL>", "company": "SURA", "organization": "SEGUROS DE VIDA SURAMERICANA S.A."}, "tickets": {"cedula": "1143936436", "fecha_consulta": "2025-06-26 14:42:15", "total_tickets": 0, "tickets_procesados": 0, "tickets": []}, "incidents": {"login_id": "1143936436", "fecha_consulta": "2025-06-26 14:42:15", "total_incidentes": 7, "incidentes_procesados": 7, "incidentes": [{"incident_id": "INC000000014189", "summary": "Aplicativo Afectado DWP: \nAplicativo o equipo afectado: OtrosDevolución de primas al cliente por cancelación de póliza BAN110256990\nRuta por la que ingresa: Aplicaciones de negocio, visor de aplicaciones, Bancaseguros\nTelefonos de afectado: 3116525252\nUbicación del afectado  (Sede y piso): El afectado es el cliente\nNombre completo del afectado: JESUS OROZCO\nUsuario con el que ingresó: lizevava\nDescripción del error: Por favor me colaboran con la validación y gestión del caso, ya que al cliente se le realizó la cancelación de la póliza BAN110256990 desde el 2025/02/21 y hasta la fecha no se le ha realizado la devolución de las primas no causadas y se le  debe de realizar la devolución por valor de 25.921 el cual ya debía haber llegado a la cuenta del cliente\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: Si\nMedio de Ingreso PQRS: Otros medios\n¿Cuál es el numero de la PQRS?: 25022134481752", "status": "Closed", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: \nAplicativo o equipo afectado: OtrosDevolución de primas al cliente por cancelación de póliza BAN110256990\nRuta por la que ingresa: Aplicaciones de negocio, visor de aplicaciones, Bancaseguros\nTelefonos de afectado: 3116525252\nUbicación del afectado  (Sede y piso): El afectado es el cliente\nNombre completo del afectado: JESUS OROZCO\nUsuario con el que ingresó: lizevava\nDescripción del error: Por favor me colaboran con la validación y gestión del caso, ya que al cliente se le realizó la cancelación de la póliza BAN110256990 desde el 2025/02/21 y hasta la fecha no se le ha realizado la devolución de las primas no causadas y se le  debe de realizar la devolución por valor de 25.921 el cual ya debía haber llegado a la cuenta del cliente\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: Si\nMedio de Ingreso PQRS: Otros medios\n¿Cuál es el numero de la PQRS?: 25022134481752", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-03619854892011EF8A165E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: PROCESO DE NEGOCIO\\nClosure Product Category Tier1: Bancaseguros\\nResolution Category Tier 2: CAPACITACION\\nClosure Product Category Tier2: Canal Banca\\nHPD_CI: SAP-AR\\nClosure Product Category Tier3: Canal Banca\\nDWP_SRID: 17360\\nDWP_SRInstanceID: AGGJYA2JFNGSXASTCTP2STCTP2E14J\\nIncident Number: INC000000014189\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SAP\\nCategorization Tier 2: FI - AR Cuentas por cobrar\\nCategorization Tier 3: Navegacion y Configuracion\\nProduct Categorization Tier 1: Bancaseguros\\nProduct Categorization Tier 3: Canal Banca\\nProduct Categorization Tier 2: Canal Banca\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000125;1000000397;'lizevava';", "last_modified_date": "2025-03-20T07:01:14.000+0000", "create_date": null, "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): proceso del area financiera de la compañia\nSolución: Se realiza contacto con la usuaria y Juan guillermo ruiz duque del area financiera para procesos de desembolsos para que le informe a la usuaria directamente porque no ha salido la devolución para el cliente, Por este canal no es posible indicarle porque no ha salido el pago dado que no es manejado por nosotros.\nCausa Raíz (Identificada/Sin identificar):Sin identificar\nConfirmar operatividad del usuario Afectado (SI/NO): NO", "resolution_method": null, "dwp_number": "17360", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nCausa (Motivo, origen o razón de que suceda el incidente): proceso del area financiera de la compañia\nSolución: Se realiza contacto con la usuaria y Juan guillermo ruiz duque del area financiera para procesos de desembolsos para que le informe a la usuaria directamente porque no ha salido la devolución para el cliente, Por este canal no es posible indicarle porque no ha salido el pago dado que no es manejado por nosotros.\nCausa Raíz (Identificada/Sin identificar):Sin identificar\nConfirmar operatividad del usuario Afectado (SI/NO): NO\n\nCategoría de resolución:\nCAPACITACION.CAPACITACION.PROCESO DE NEGOCIO", "last_modified_date": "2025-03-14T19:55:58.000+0000"}}, {"incident_id": "INC000000016058", "summary": "Aplicativo Afectado: SitiosWeb\nNombre completo del afectado: SANTIAGO HENAO\nUsuario de red: lizevava\nTeléfono del afectado: 3023015635\nCorreo del afectado: <EMAIL>\nUbicación del afectado: CASA\nRuta por la que ingresa: APP DIGITAL SEGUROS SURA\nAplicativo afectado: Seguros Sura\n¿Se autenticó en el sitio?: \nPerfil: \nOperación: HE CAMBIADO LA CONTRASEÑA Y AL USAR LA APP SEGUROS SURA ME SALE PARA INGRESAR CON HUELLA PERO NO INGRESA Y ARROJA ERROR. SI CIERRO LA VENTANA EMERGENTE DE LA SOLICITD DE HUELLA SE CIERRA TAMBIÉN EL TECLADO Y NO PERMITE INGRESAR LA CONTRASEÑA. SI TOCO EL AREA DE LA CONTRASEÑA VUELVE A ABRIR EL INGRESO POR HUELLA EN LUGAR DE SÓLO ABRIR EL TECLADO. LOGR<PERSON>DO QUE NO SE PUEDA INGRESAR.\nURL: APP SEGUROS SURA\nNavegador y versión: \nDescripción del error: HE CAMBIADO LA CONTRASEÑA Y AL USAR LA APP SEGUROS SURA ME SALE PARA INGRESAR CON HUELLA PERO NO INGRESA Y ARROJA ERROR. SI CIERRO LA VENTANA EMERGENTE DE LA SOLICITD DE HUELLA SE CIERRA TAMBIÉN EL TECLADO Y NO PERMITE INGRESAR LA CONTRASEÑA. SI TOCO EL ÁREA DE LA CONTRASEÑA VUELVE A ABRIR EL INGRESO POR HUELLA EN LUGAR DE SOLO ABRIR EL TECLADO. LOGRANDO QUE NO SE PUEDA INGRESAR.\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: Si\nMedio de Ingreso: Otros medios\n¿Cuál es el numero de la PQRS?: 25031134615799", "status": "Closed", "description": "Sitios Web", "detailed_description": "Aplicativo Afectado: SitiosWeb\nNombre completo del afectado: SANTIAGO HENAO\nUsuario de red: lizevava\nTeléfono del afectado: 3023015635\nCorreo del afectado: <EMAIL>\nUbicación del afectado: CASA\nRuta por la que ingresa: APP DIGITAL SEGUROS SURA\nAplicativo afectado: Seguros Sura\n¿Se autenticó en el sitio?: \nPerfil: \nOperación: HE CAMBIADO LA CONTRASEÑA Y AL USAR LA APP SEGUROS SURA ME SALE PARA INGRESAR CON HUELLA PERO NO INGRESA Y ARROJA ERROR. SI CIERRO LA VENTANA EMERGENTE DE LA SOLICITD DE HUELLA SE CIERRA TAMBIÉN EL TECLADO Y NO PERMITE INGRESAR LA CONTRASEÑA. SI TOCO EL AREA DE LA CONTRASEÑA VUELVE A ABRIR EL INGRESO POR HUELLA EN LUGAR DE SÓLO ABRIR EL TECLADO. LOGR<PERSON>DO QUE NO SE PUEDA INGRESAR.\nURL: APP SEGUROS SURA\nNavegador y versión: \nDescripción del error: HE CAMBIADO LA CONTRASEÑA Y AL USAR LA APP SEGUROS SURA ME SALE PARA INGRESAR CON HUELLA PERO NO INGRESA Y ARROJA ERROR. SI CIERRO LA VENTANA EMERGENTE DE LA SOLICITD DE HUELLA SE CIERRA TAMBIÉN EL TECLADO Y NO PERMITE INGRESAR LA CONTRASEÑA. SI TOCO EL ÁREA DE LA CONTRASEÑA VUELVE A ABRIR EL INGRESO POR HUELLA EN LUGAR DE SOLO ABRIR EL TECLADO. LOGRANDO QUE NO SE PUEDA INGRESAR.\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: Si\nMedio de Ingreso: Otros medios\n¿Cuál es el numero de la PQRS?: 25031134615799", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-02161B96892011EF88EA5E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: VERSION\\nClosure Product Category Tier1: SuperAPP\\nResolution Category Tier 2: ACTUALIZAR\\nClosure Product Category Tier2: SuperAPP\\nHPD_CI: SuperAPP\\nClosure Product Category Tier3: SuperAPP\\nDWP_SRID: 19867\\nDWP_SRInstanceID: AGGEFKK2DFA3IAST1NNUST1NNULJ7N\\nIncident Number: INC000000016058\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: SuperAPP\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: SuperAPP\\nProduct Categorization Tier 3: SuperAPP\\nProduct Categorization Tier 2: SuperAPP\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000518;1000000387;'lizevava';", "last_modified_date": "2025-05-06T03:52:45.000+0000", "create_date": null, "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Se actualizo version con ajuste en modulo de biometricos que resolvio la falla\nSe pregunto al usuario y esta operativo", "resolution_method": null, "dwp_number": "19867", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Se adjunta apribacion del cliente", "last_modified_date": "2025-04-02T14:13:09.000+0000"}}, {"incident_id": "INC000000016498", "summary": "Aplicativo Afectado: SitiosWeb\nNombre completo del afectado: EDWIN GUERRERO\nUsuario de red: ES UN CLIENTE\nTeléfono del afectado: 3103231394\nCorreo del afectado: <EMAIL>\nUbicación del afectado: CASA CLIENTE\nRuta por la que ingresa: APP SEGUROS SURA\nAplicativo afectado: Seguros Sura\n¿Se autenticó en el sitio?: \nPerfil: \nOperación: EL CLIENTE ESTA INTENTANDO INGRESAR A LA APP A SU POLIZA DE HOGAR Y NO LE PERMITE\nURL: APP SEGUROS\nNavegador y versión: APP\nDescripción del error: CLIENTE SE ENCUENTRA INCONFORME, YA QUE EN LA APP NO LE APARECE SU PÓLIZA HOGAR.\nPÓLIZA 900001130910\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: Si\nMedio de Ingreso: Otros medios\n¿Cuál es el numero de la PQRS?: 25030734592536", "status": "Cancelled", "description": "Sitios Web", "detailed_description": "Aplicativo Afectado: SitiosWeb\nNombre completo del afectado: EDWIN GUERRERO\nUsuario de red: ES UN CLIENTE\nTeléfono del afectado: 3103231394\nCorreo del afectado: <EMAIL>\nUbicación del afectado: CASA CLIENTE\nRuta por la que ingresa: APP SEGUROS SURA\nAplicativo afectado: Seguros Sura\n¿Se autenticó en el sitio?: \nPerfil: \nOperación: EL CLIENTE ESTA INTENTANDO INGRESAR A LA APP A SU POLIZA DE HOGAR Y NO LE PERMITE\nURL: APP SEGUROS\nNavegador y versión: APP\nDescripción del error: CLIENTE SE ENCUENTRA INCONFORME, YA QUE EN LA APP NO LE APARECE SU PÓLIZA HOGAR.\nPÓLIZA 900001130910\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: Si\nMedio de Ingreso: Otros medios\n¿Cuál es el numero de la PQRS?: 25030734592536", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-01D93348892011EF88AE5E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: NO EXITOSO\\nClosure Product Category Tier1: Sucursal Virtual Personas\\nResolution Category Tier 2: CONTACTO\\nClosure Product Category Tier2: Sucursal Virtual Personas\\nHPD_CI: PortalesAPI\\nClosure Product Category Tier3: Sucursal Virtual Personas\\nDWP_SRID: 20778\\nDWP_SRInstanceID: AGGEFKK2DFA3IAST2O4VST2O4VN2WK\\nIncident Number: INC000000016498\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SUR\\nCategorization Tier 2: Sucursal Virtual Personas\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: Sucursal Virtual Empresas\\nProduct Categorization Tier 3: Sucursal Virtual Empresas\\nProduct Categorization Tier 2: Sucursal Virtual Empresas\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000518;'lizevava';", "last_modified_date": "2025-06-03T02:20:50.000+0000", "create_date": null, "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": null, "resolution_method": null, "dwp_number": "20778", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Justificación de cancelado: Buen día\n\nDesde la SVP y validación con Lizeth quien levanto el incidente, no se ha tenido novedad con el usuario que reporta el tema de poliza hogar.\n\nMuchas Gracias", "last_modified_date": "2025-04-24T13:47:47.000+0000"}}, {"incident_id": "INC000000051808", "summary": "Aplicativo Afectado DWP: \nAplicativo o equipo afectado: OtrosAPP CLIENTE SEGUROS SURA\nRuta por la que ingresa: APP DESDE EL DISPOSITIVO MOVIL\nTelefonos de afectado: 3176361237\nUbicación del afectado  (Sede y piso): CLIENTE  EN MEDELLIN\nNombre completo del afectado: YULIANA ANDREA ACEVEDO HERNANDEZ\nUsuario con el que ingresó: 1152195360\nDescripción del error: EL CLIENTE NOS INDICA QUE: DESDE QUE ADQUIRÍ MI PÓLIZA DESCARGUÉ LA APP DE SURA PARA PROGRAMAR LAS CITAS POR ESTE MEDIO. REALMENTE, NUNCA HE PODIDO REALIZAR ESA AUTOGESTIÓN, PORQUE LA APP NUNCA ME LO PERMITE Y SIEMPRE TERMINO LLAMANDO AL #888 PARA AGENDAR. REQUIERO POR FAVOR SU AYUDA PARA QUÉ PUEDA REALIZAR LAS AUTOGESTIONES.\n\nCLIENTE CUENTA CON POLIZA PLAN SALUD EVOLUCIONA FAMILIAR\n\nMUCHAS GRACIAS, QUEDO MUY ATENTA.\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: Si\nMedio de Ingreso PQRS: Otros medios\n¿Cuál es el numero de la PQRS?: 25052335159995", "status": "Assigned", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: \nAplicativo o equipo afectado: OtrosAPP CLIENTE SEGUROS SURA\nRuta por la que ingresa: APP DESDE EL DISPOSITIVO MOVIL\nTelefonos de afectado: 3176361237\nUbicación del afectado  (Sede y piso): CLIENTE  EN MEDELLIN\nNombre completo del afectado: YULIANA ANDREA ACEVEDO HERNANDEZ\nUsuario con el que ingresó: 1152195360\nDescripción del error: EL CLIENTE NOS INDICA QUE: DESDE QUE ADQUIRÍ MI PÓLIZA DESCARGUÉ LA APP DE SURA PARA PROGRAMAR LAS CITAS POR ESTE MEDIO. REALMENTE, NUNCA HE PODIDO REALIZAR ESA AUTOGESTIÓN, PORQUE LA APP NUNCA ME LO PERMITE Y SIEMPRE TERMINO LLAMANDO AL #888 PARA AGENDAR. REQUIERO POR FAVOR SU AYUDA PARA QUÉ PUEDA REALIZAR LAS AUTOGESTIONES.\n\nCLIENTE CUENTA CON POLIZA PLAN SALUD EVOLUCIONA FAMILIAR\n\nMUCHAS GRACIAS, QUEDO MUY ATENTA.\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: Si\nMedio de Ingreso PQRS: Otros medios\n¿Cuál es el numero de la PQRS?: 25052335159995", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-02161B96892011EF88EA5E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nClosure Product Category Tier1: Aplicaciones\\nClosure Product Category Tier2: Aplicaciones de negocio\\nHPD_CI: SuperAPP\\nClosure Product Category Tier3: Aplicaciones de negocio\\nDWP_SRID: 65883\\nDWP_SRInstanceID: AGGJYG8IF3BYEASWX9TYSWX9TYBO3O\\nIncident Number: INC000000051808\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: SuperAPP\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: SuperAPP\\nProduct Categorization Tier 3: SuperAPP\\nProduct Categorization Tier 2: SuperAPP\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000125;1000000319;'lizevava';", "last_modified_date": "2025-06-09T13:23:54.000+0000", "create_date": null, "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "65883", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "se reviso en conjunto con <PERSON> y con <PERSON> y se evidenció que no es un tema de parametrización, lo que pasa es que cuando el usuario tiene 2 pólizas ( en este caso los usuarios reportados tienen póliza juvenil y Evoluciona), está generando un error porque está intentando asignar la cita por la póliza juvenil. \nEste ajuste requiere un raizal, se va a dar prioridad y la idea es salir a producción el día lunes.\nPara estos 3 casos se va a intentar suprimirlos por base de datos y que devuelva primero la póliza evoluciona y no la juvenil.", "last_modified_date": "2025-06-04T16:11:31.000+0000"}}, {"incident_id": "INC000000051852", "summary": "Aplicativo Afectado DWP: \nAplicativo o equipo afectado: OtrosAPP SEGUROS SURA\nRuta por la que ingresa: APP DESDE DISPOSITIVO MOVIL\nTelefonos de afectado: 3135607163\nUbicación del afectado  (Sede y piso): CASA ES UN CLIENTE\nNombre completo del afectado: SARA PARRA\nUsuario con el que ingresó: 1015076609\nDescripción del error: ES LA SEGUNDA VEZ, EN MENOS DE 8 DÍAS QUE INTENTO ASIGNAR UNA CITA NO PROGRAMADA PARA MI HIJA (TI 1015076609) POR LA PÁGINA Y APP Y ME SALE EL ERROR QUE DICE ALGO COMO \"EL PACIENTE NO SE ENCUENTRA ASOCIADO A NINGÚN PLAN), PERO SI SELECCIONO UNA CITA PROGRAMADA SI ME DEJA HACERLO.\n\nNO SÉ SI ES UN ERROR DEL SISTEMA YA QUE LA BENEFICIARIA ES MI HIJA Y YO SOY EL CLIENTE, PERO ESTO ANTES FUNCIONABA BIEN.\n\nLA SEMANA PASADA FUE LO MISMO, LLAMÉ Y ME DIJERON QUE ERA UNA SUPUESTA “ACTUALIZACIÓN DEL SISTEMA” Y QUE SIGUIERA PROBANDO, PERO YA VEO QUE ES MENTIRA, Y DESCARADAMENTE AYER 22 DE MAYO ME LLAMO UNA PERSONA DE SERVICIO AL CLIENTE DE SURA PARA PEDIR DISCULPA Y DEMÁS POR LA QUEJA QUE PUSE POR TELÉFONO. PERO VEO QUE REALMENTE NO HICIERON NADA Y EL PROBLEMA PERSISTE Y ME ESTÁN NEGANDO ESTE SERVICIO QUE ESTÁN OFERTANDO.\n\nPOR FAVOR NECESITO QUE ME CORRIJAN ESTE PROBLEMA YA QUE YO COMO CLIENTE DE SURA ESTOY PAGANDO POR UN SERVICIO COMPLETO Y LA IDEA ES QUE MI HIJA POR MEDIO DE LA PÓLIZA PUEDA ACCEDER A LAS CITAS PROGRAMADAS Y NO PROGRAMADAS DE LAS SEDES EN MEDELLÍN SIEMPRE Y CUANDO HAYA DISPONIBILIDAD, PERO USTEDES LE ESTÁN NEGANDO ESTA OPCIÓN DE SERVICIO QUE COMO SU NOMBRE LO DICE, ESTOY PRE-PAGANDO.\n\nNECESITO POR FAVOR UNA RESPUESTA DE FONDO, NO SOLO DISCULPAS NI MENTIRAS, SINO LA PRÓXIMA QUEJA LA DIRIJO CON COPIA AL ENTE DE CONTROL QUE REGULA SEGUROS SURA.\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: Si\nMedio de Ingreso PQRS: Otros medios\n¿Cuál es el numero de la PQRS?: 25052335159344", "status": "Assigned", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: \nAplicativo o equipo afectado: OtrosAPP SEGUROS SURA\nRuta por la que ingresa: APP DESDE DISPOSITIVO MOVIL\nTelefonos de afectado: 3135607163\nUbicación del afectado  (Sede y piso): CASA ES UN CLIENTE\nNombre completo del afectado: SARA PARRA\nUsuario con el que ingresó: 1015076609\nDescripción del error: ES LA SEGUNDA VEZ, EN MENOS DE 8 DÍAS QUE INTENTO ASIGNAR UNA CITA NO PROGRAMADA PARA MI HIJA (TI 1015076609) POR LA PÁGINA Y APP Y ME SALE EL ERROR QUE DICE ALGO COMO \"EL PACIENTE NO SE ENCUENTRA ASOCIADO A NINGÚN PLAN), PERO SI SELECCIONO UNA CITA PROGRAMADA SI ME DEJA HACERLO.\n\nNO SÉ SI ES UN ERROR DEL SISTEMA YA QUE LA BENEFICIARIA ES MI HIJA Y YO SOY EL CLIENTE, PERO ESTO ANTES FUNCIONABA BIEN.\n\nLA SEMANA PASADA FUE LO MISMO, LLAMÉ Y ME DIJERON QUE ERA UNA SUPUESTA “ACTUALIZACIÓN DEL SISTEMA” Y QUE SIGUIERA PROBANDO, PERO YA VEO QUE ES MENTIRA, Y DESCARADAMENTE AYER 22 DE MAYO ME LLAMO UNA PERSONA DE SERVICIO AL CLIENTE DE SURA PARA PEDIR DISCULPA Y DEMÁS POR LA QUEJA QUE PUSE POR TELÉFONO. PERO VEO QUE REALMENTE NO HICIERON NADA Y EL PROBLEMA PERSISTE Y ME ESTÁN NEGANDO ESTE SERVICIO QUE ESTÁN OFERTANDO.\n\nPOR FAVOR NECESITO QUE ME CORRIJAN ESTE PROBLEMA YA QUE YO COMO CLIENTE DE SURA ESTOY PAGANDO POR UN SERVICIO COMPLETO Y LA IDEA ES QUE MI HIJA POR MEDIO DE LA PÓLIZA PUEDA ACCEDER A LAS CITAS PROGRAMADAS Y NO PROGRAMADAS DE LAS SEDES EN MEDELLÍN SIEMPRE Y CUANDO HAYA DISPONIBILIDAD, PERO USTEDES LE ESTÁN NEGANDO ESTA OPCIÓN DE SERVICIO QUE COMO SU NOMBRE LO DICE, ESTOY PRE-PAGANDO.\n\nNECESITO POR FAVOR UNA RESPUESTA DE FONDO, NO SOLO DISCULPAS NI MENTIRAS, SINO LA PRÓXIMA QUEJA LA DIRIJO CON COPIA AL ENTE DE CONTROL QUE REGULA SEGUROS SURA.\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: Si\nMedio de Ingreso PQRS: Otros medios\n¿Cuál es el numero de la PQRS?: 25052335159344", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-02161B96892011EF88EA5E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nHPD_CI: SuperAPP\\nDWP_SRID: 65957\\nDWP_SRInstanceID: AGGJYG8IF3BYEASWXPYDSWXPYDGYPP\\nIncident Number: INC000000051852\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: SuperAPP\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: SuperAPP\\nProduct Categorization Tier 3: SuperAPP\\nProduct Categorization Tier 2: SuperAPP\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000125;1000000319;'lizevava';", "last_modified_date": "2025-06-09T13:24:21.000+0000", "create_date": null, "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "65957", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "se reviso en conjunto con <PERSON> y con <PERSON> y se evidenció que no es un tema de parametrización, lo que pasa es que cuando el usuario tiene 2 pólizas ( en este caso los usuarios reportados tienen póliza juvenil y Evoluciona), está generando un error porque está intentando asignar la cita por la póliza juvenil. \nEste ajuste requiere un raizal, se va a dar prioridad y la idea es salir a producción el día lunes.\nPara estos 3 casos se va a intentar suprimirlos por base de datos y que devuelva primero la póliza evoluciona y no la juvenil.", "last_modified_date": "2025-06-04T16:12:21.000+0000"}}, {"incident_id": "INC000000051737", "summary": "Aplicativo Afectado DWP: \nAplicativo o equipo afectado: OtrosAPP SEGUROS SURA\nRuta por la que ingresa: APP SEGUROS SURA CLIENTE\nTelefonos de afectado: 3008274382\nUbicación del afectado  (Sede y piso): CLIENTE\nNombre completo del afectado: LAURENT <PERSON><PERSON><PERSON>A BALLESTEROS QUINTANA\nUsuario con el que ingresó: 1020228782\nDescripción del error: CLIENTE NOS INDICA QUE: INCONVENIENTE CON LA APP DE SEGUROS SURA, PARA SOLICITUD DE CITAS EN LÍNEA DE ESPECIALISTAS DE LA PÓLIZA SALUD PARA TODOS INTEGRAL PREFERENCIAL DE MI HIJA LAURENT SOFIA BALLESTEROS QUINTANA, REGISTRA CON UN CENTRO MÉDICO DE BOGOTÁ Y DARSALUD, NUNCA HEMOS RESIDIDO EN ESA CIUDAD Y NO PERMITE CAMBIAR LAS OPCIONES A OTRA CIUDAD, ADJUNTO PANTALLAZO DE LA APP Y NOS PASA CON EL INGRESO A LOS DOS COTIZANTES. LOS DATOS ESTÁN ACTUALIZADOS Y ELLA REGISTRA CON ATENCIÓN EN IPS MOLINOS, POR LA PÁGINA EN LÍNEA SI PERMITE EL PROCESO, PERO EN LA APP NO. HEMOS BORRADO LA APLICACIÓN, DESCARGADO DE NUEVO Y EL INCONVENIENTE CONTINUA.\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: Si\nMedio de Ingreso PQRS: Otros medios\n¿Cuál es el numero de la PQRS?: 25052335159867", "status": "Closed", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: \nAplicativo o equipo afectado: OtrosAPP SEGUROS SURA\nRuta por la que ingresa: APP SEGUROS SURA CLIENTE\nTelefonos de afectado: 3008274382\nUbicación del afectado  (Sede y piso): CLIENTE\nNombre completo del afectado: LAURENT <PERSON><PERSON><PERSON>A BALLESTEROS QUINTANA\nUsuario con el que ingresó: 1020228782\nDescripción del error: CLIENTE NOS INDICA QUE: INCONVENIENTE CON LA APP DE SEGUROS SURA, PARA SOLICITUD DE CITAS EN LÍNEA DE ESPECIALISTAS DE LA PÓLIZA SALUD PARA TODOS INTEGRAL PREFERENCIAL DE MI HIJA LAURENT SOFIA BALLESTEROS QUINTANA, REGISTRA CON UN CENTRO MÉDICO DE BOGOTÁ Y DARSALUD, NUNCA HEMOS RESIDIDO EN ESA CIUDAD Y NO PERMITE CAMBIAR LAS OPCIONES A OTRA CIUDAD, ADJUNTO PANTALLAZO DE LA APP Y NOS PASA CON EL INGRESO A LOS DOS COTIZANTES. LOS DATOS ESTÁN ACTUALIZADOS Y ELLA REGISTRA CON ATENCIÓN EN IPS MOLINOS, POR LA PÁGINA EN LÍNEA SI PERMITE EL PROCESO, PERO EN LA APP NO. HEMOS BORRADO LA APLICACIÓN, DESCARGADO DE NUEVO Y EL INCONVENIENTE CONTINUA.\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: Si\nMedio de Ingreso PQRS: Otros medios\n¿Cuál es el numero de la PQRS?: 25052335159867", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-02161B96892011EF88EA5E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nResolution Category Tier 3: APLICATIVOS\\nClosure Product Category Tier1: SuperAPP\\nResolution Category Tier 2: CAPACITACION\\nClosure Product Category Tier2: SuperAPP\\nHPD_CI: SuperAPP\\nClosure Product Category Tier3: SuperAPP\\nDWP_SRID: 65778\\nDWP_SRInstanceID: AGGHE4WHFZM13ASWXKEXSWXKEXPTD4\\nIncident Number: INC000000051737\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: SuperAPP\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: SuperAPP\\nProduct Categorization Tier 3: SuperAPP\\nProduct Categorization Tier 2: SuperAPP\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000125;1000000387;'lizevava';", "last_modified_date": "2025-06-02T07:03:24.000+0000", "create_date": null, "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): La niña Laurent Ballesteros aparece en el grupo familiar del cliente con documento\": \"11809403\", \"CARLOS ALBERTO\" \"BALLESTEROS GUTIERREZ\", y por ser el el titular de la poliza especial 090001292948 es quien puede agendarle citas medicas a la niña.  Necesitamos saber que persona necesita agendarle citas porque en la descripción de la falla no aparece documento y ademas cuando dice que la pagina en linea, si le permite se refiere a la sucursal virtual personas Sura o a la de EPS?\nSolución: Capacitacion aplicativos\nCausa Raíz (Identificada/Sin identificar):identificada\nConfirmar operatividad del usuario Afectado (SI/NO):", "resolution_method": null, "dwp_number": "65778", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "La niña Laurent Ballesteros aparece en el grupo familiar del cliente con documento\": \"11809403\", \"CARLOS ALBERTO\" \"BALLESTEROS GUTIERREZ\", y por ser el el titular de la poliza especial 090001292948 es quien puede agendarle citas medicas a la niña.  Necesitamos saber que persona necesita agendarle citas porque en la descripción de la falla no aparece documento y ademas cuando dice que la pagina en linea, si le permite se refiere a la sucursal virtual personas Sura o a la de EPS?", "last_modified_date": "2025-05-27T19:42:39.000+0000"}}, {"incident_id": "INC000000052762", "summary": "Aplicativo Afectado DWP: \nAplicativo o equipo afectado: OtrosPAGINA DE SEGUROS SURA\nRuta por la que ingresa: PAGINA DE SEGUROS SURA\nTelefonos de afectado: 3166241400\nUbicación del afectado  (Sede y piso): CLIENTE CASA\nNombre completo del afectado: MATEO JARAMILLO OCHOA\nUsuario con el que ingresó: 1053844757\nDescripción del error: BUEN DÍA, AGRADEZCO SU APOYO CORRIGIENDO EL CAMPO DE SEXO DE NACIMIENTO QUE APARECE EN LA PÁGINA DE SURA COMO FEMENINO.\nAD<PERSON>UNTO FOTO DE LA CÉDULA, EN DONDE DEBERÍA APARECER COMO MASCULINO.\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: Si\nMedio de Ingreso PQRS: Otros medios\n¿Cuál es el numero de la PQRS?: 25052635173710", "status": "Assigned", "description": "Otras Aps Empresarial", "detailed_description": "Aplicativo Afectado DWP: \nAplicativo o equipo afectado: OtrosPAGINA DE SEGUROS SURA\nRuta por la que ingresa: PAGINA DE SEGUROS SURA\nTelefonos de afectado: 3166241400\nUbicación del afectado  (Sede y piso): CLIENTE CASA\nNombre completo del afectado: MATEO JARAMILLO OCHOA\nUsuario con el que ingresó: 1053844757\nDescripción del error: BUEN DÍA, AGRADEZCO SU APOYO CORRIGIENDO EL CAMPO DE SEXO DE NACIMIENTO QUE APARECE EN LA PÁGINA DE SURA COMO FEMENINO.\nAD<PERSON>UNTO FOTO DE LA CÉDULA, EN DONDE DEBERÍA APARECER COMO MASCULINO.\n¿Cuántas personas hay afectadas?: 1 persona\n¿El reporte surge de una PQRS de Salesforce?: Si\nMedio de Ingreso PQRS: Otros medios\n¿Cuál es el numero de la PQRS?: 25052635173710", "technical_info": "z1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-0374265E892011EF8A285E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nClosure Product Category Tier1: Aplicaciones\\nClosure Product Category Tier2: Aplicaciones de negocio\\nHPD_CI: CCM\\nClosure Product Category Tier3: Aplicaciones de negocio\\nDWP_SRID: 67157\\nDWP_SRInstanceID: AGGJYG8IF3BYEASWZYO5SWZYO5DP8G\\nIncident Number: INC000000052762\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: CCM\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: CCM\\nProduct Categorization Tier 3: Gestión de comunicaciones\\nProduct Categorization Tier 2: Transversales\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000125;1000000379;'lizevava';", "last_modified_date": "2025-06-09T21:30:16.000+0000", "create_date": null, "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "67157", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Buena tarde,\n\n*Se válida los datos\n*Se valida imágenes anexas\n*Se escala caso al único analista del grupo\n*Se escala caso para su amable gestión\n\nFeliz día", "last_modified_date": "2025-05-29T00:58:51.000+0000"}}]}}