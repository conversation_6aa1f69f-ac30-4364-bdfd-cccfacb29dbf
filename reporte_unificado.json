{"tickets": {"cedula": "1143936436", "fecha_consulta": "2025-06-26 12:46:33", "total_tickets": 0, "tickets_procesados": 0, "tickets": []}, "incidents": {"login_id": "1143936436", "fecha_consulta": "2025-06-26 12:46:33", "total_incidentes": 5, "incidentes_procesados": 5, "incidentes": [{"incident_id": "INC000000008763", "summary": "Clave de red", "status": "Closed", "detailed_description": "Sin resumen disponible", "assigned_to": null, "assignee_groups": "1000000124;'lizegato';", "last_modified_date": "2025-03-10T07:01:29.000+0000", "create_date": null, "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Buenos días\n\nSe le indica al usuario que se debe comunicar el líder de TP para poder solicitar un restablecimiento de contraseña", "resolution_method": null, "dwp_number": "8796", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nBuenos días\n\nSe le indica al usuario que se debe comunicar el líder de TP para poder solicitar un restablecimiento de contraseña\n\nCategoría de resolución:\nCAPACITACION.CAPACITACION.APLICATIVOS", "last_modified_date": "2025-03-04T13:23:42.000+0000"}}, {"incident_id": "INC000000009105", "summary": "Plantilla General", "status": "Closed", "detailed_description": "Sin resumen disponible", "assigned_to": null, "assignee_groups": "1000000124;1000000155;'lizegato';", "last_modified_date": "2025-03-11T07:00:25.000+0000", "create_date": null, "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Buen día\n \n*Se comunica la usuaria a la línea de Bloqueos\n*Se valida los datos del caso\n*Se valida el estado de la cuenta desde el DA y aparece la cuenta Expirada\n*Se válida con la compañera del área de accesos K.C\n*Se hace la prueba con la usuaria y funciona\n*Usuaria queda operativa\n*Se procede con el cierre del caso\n \nSaludos", "resolution_method": null, "dwp_number": "9279", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nBuen día\n \n*Se comunica la usuaria a la línea de Bloqueos\n*Se valida los datos del caso\n*Se valida el estado de la cuenta desde el DA y aparece la cuenta Expirada\n*Se válida con la compañera del área de accesos K.C\n*Se hace la prueba con la usuaria y funciona\n*Usuaria queda operativa\n*Se procede con el cierre del caso\n \nSaludos\n\nCategoría de resolución:\nGESTION DE CUENTAS DE USUARIO.ACTIVAR.USUARIO", "last_modified_date": "2025-03-05T20:47:14.000+0000"}}, {"incident_id": "INC000000012374", "summary": "Autenticador", "status": "Closed", "detailed_description": "Sin resumen disponible", "assigned_to": null, "assignee_groups": "1000000124;1000000135;'lizeusbo';", "last_modified_date": "2025-03-17T07:01:45.000+0000", "create_date": null, "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Cordial saludo,\n \n* Usuario se comunica a la línea de MFA.\n* Se restablece configuración MFA desde el Equipo SOC.\n* Se confirma operatividad por parte del agente de mesa que atendió la llamada.\n* Usuario autoriza cierre del incidente.\n* Se procede al cierre del incidente.\n \nMuchas gracias", "resolution_method": null, "dwp_number": "14710", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nCordial saludo,\n \n* Usuario se comunica a la línea de MFA.\n* Se restablece configuración MFA desde el Equipo SOC.\n* Se confirma operatividad por parte del agente de mesa que atendió la llamada.\n* Usuario autoriza cierre del incidente.\n* Se procede al cierre del incidente.\n \nMuchas gracias\n\nCategoría de resolución:\nGESTION DE CUENTAS DE USUARIO.REESTABLECIMIENTO.MFA", "last_modified_date": "2025-03-11T14:30:18.000+0000"}}, {"incident_id": "INC000000013114", "summary": "No le aparece la orden en SAP para facturar.", "status": "Closed", "detailed_description": "Sin resumen disponible", "assigned_to": null, "assignee_groups": "1000000134;1000000400;'lizecosl';", "last_modified_date": "2025-03-20T07:00:28.000+0000", "create_date": null, "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Buen día, se reprocesa orden 932-1307456100 y viaja a sap con número de autorización 9031995739", "resolution_method": null, "dwp_number": "15698", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nBuen día, se reprocesa orden 932-1307456100 y viaja a sap con número de autorización 9031995739\n\nCategoría de resolución:\nMODIFICACIONES.MODIFICAR.DATO DEFINICION DE NEGOCIO", "last_modified_date": "2025-03-14T19:15:31.000+0000"}}, {"incident_id": "INC000000017209", "summary": "restablecer mfa", "status": "Closed", "detailed_description": "Sin resumen disponible", "assigned_to": null, "assignee_groups": "1000000124;'lizegato';", "last_modified_date": "2025-03-26T07:01:36.000+0000", "create_date": null, "priority": "Low", "impact": "4-Minor/Localized", "urgency": "4-<PERSON>", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Buen día\n\n*Se comunica el usuario a la línea de MFA\n*Se valida los datos del usuario\n*Se solicita restablecimiento del MFA\n*Se hace pruebas y funciona\n*Usuario autoriza el cierre del caso\n\nFeliz día", "resolution_method": null, "dwp_number": "21595", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "Solución de la petición:\nBuen día\n\n*Se comunica el usuario a la línea de MFA\n*Se valida los datos del usuario\n*Se solicita restablecimiento del MFA\n*Se hace pruebas y funciona\n*Usuario autoriza el cierre del caso\n\nFeliz día\n\nCategoría de resolución:\nGESTION DE CUENTAS DE USUARIO.REESTABLECIMIENTO.MFA", "last_modified_date": "2025-03-20T11:11:24.000+0000"}}]}}