{"user_info": {"cedula": "65883", "found": false, "first_name": "", "last_name": "", "full_name": "", "email": "", "company": "", "organization": ""}, "tickets": {"cedula": "65883", "fecha_consulta": "2025-06-26 13:29:48", "total_tickets": 0, "tickets_procesados": 0, "tickets": []}, "incidents": {"login_id": "65883", "fecha_consulta": "2025-06-26 13:29:48", "total_incidentes": 1, "incidentes_procesados": 1, "incidentes": [{"incident_id": "INC000000051808", "summary": "Otras Aps Empresarial", "status": "Assigned", "detailed_description": "Descripción: Otras Aps Empresarial\\n\\nInformación de Aplicativos/Equipos:\\nz1D_CI_FormName: AST:Application\\nHPD_CI_ReconID: OI-02161B96892011EF88EA5E0313B2FF74\\nHPD_CI_FormName: BMC_APPLICATION\\nAssociated Alarm: None\\nClosure Product Category Tier1: Aplicaciones\\nClosure Product Category Tier2: Aplicaciones de negocio\\nHPD_CI: SuperAPP\\nClosure Product Category Tier3: Aplicaciones de negocio\\nDWP_SRID: 65883\\nDWP_SRInstanceID: AGGJYG8IF3BYEASWX9TYSWX9TYBO3O\\nIncident Number: INC000000051808\\nRequestCreatedFromDWP: Yes\\nCity: MEDELLIN\\nCategorization Tier 1: SEG\\nCategorization Tier 2: SuperAPP\\nCategorization Tier 3: Errores Aplicacion\\nProduct Categorization Tier 1: SuperAPP\\nProduct Categorization Tier 3: SuperAPP\\nProduct Categorization Tier 2: SuperAPP\\nService Type: User Service Restoration", "assigned_to": null, "assignee_groups": "1000000125;1000000319;'lizevava';", "last_modified_date": "2025-06-09T13:23:54.000+0000", "create_date": null, "priority": "Critical", "impact": "1-Extensive/Widespread", "urgency": "1-Critical", "customer": "", "login_id": null, "service_type": "User Service Restoration", "service": null, "region": "Antioquia", "site_group": "Antioquia", "resolution": "Causa (Motivo, origen o razón de que suceda el incidente): \nSolución: \nCausa <PERSON> (Identificada/Sin identificar):\nConfirmar operatividad del usuario Afectado (SI/NO): ", "resolution_method": null, "dwp_number": "65883", "last_note": {"note_number": 1, "description": "<PERSON> resumen", "detailed_description": "se reviso en conjunto con <PERSON> y con <PERSON> y se evidenció que no es un tema de parametrización, lo que pasa es que cuando el usuario tiene 2 pólizas ( en este caso los usuarios reportados tienen póliza juvenil y Evoluciona), está generando un error porque está intentando asignar la cita por la póliza juvenil. \nEste ajuste requiere un raizal, se va a dar prioridad y la idea es salir a producción el día lunes.\nPara estos 3 casos se va a intentar suprimirlos por base de datos y que devuelva primero la póliza evoluciona y no la juvenil.", "last_modified_date": "2025-06-04T16:11:31.000+0000"}}]}}