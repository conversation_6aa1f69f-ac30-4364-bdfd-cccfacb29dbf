import json
import os
import sys
import datetime
import requests
from prettytable import PrettyTable
from colorama import init, Fore, Style
from typing import Optional, Dict, List, Any, Tuple
from pathlib import Path

from a import TicketManager
from config import get_config, is_silent_mode
from logging_config import get_logger, log_operation_start, log_operation_success, log_operation_error, log_api_call

# Inicializar colorama para colores en la consola
init()

from display import create_displayer

def mostrar_reporte_unificado(json_file: str, mostrar_todos: bool = False):
    """
    Muestra el reporte unificado con tablas en la consola.

    Args:
        json_file: Path to the JSON report file
        mostrar_todos: Whether to show all incidents or limit to 5
    """
    logger = get_logger('open.mostrar_reporte')
    displayer = create_displayer()

    try:
        log_operation_start(logger, "mostrar reporte", archivo=json_file, mostrar_todos=mostrar_todos)

        # Verificar si el archivo existe
        if not os.path.exists(json_file):
            displayer.display_error(f"El archivo {json_file} no existe.")
            return

        # Cargar el archivo JSON
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # Mostrar información de tickets
        _display_tickets_section(data, displayer, logger)

        # Mostrar información de incidentes
        _display_incidents_section(data, displayer, logger, mostrar_todos)

        log_operation_success(logger, "mostrar reporte", archivo=json_file)

    except json.JSONDecodeError as e:
        log_operation_error(logger, "mostrar reporte", e, archivo=json_file, error_type="JSON inválido")
        displayer.display_error("El archivo no es un JSON válido.")
    except Exception as e:
        log_operation_error(logger, "mostrar reporte", e, archivo=json_file)
        displayer.display_error(str(e))

def buscar_tickets_por_cedula(cedula: str, todos: bool = False) -> Optional[str]:
    """
    Busca tickets e incidentes para una cédula específica en BMC Remedy.

    Args:
        cedula: Número de cédula a consultar
        todos: Si es True, incluye todos los incidentes

    Returns:
        Path to the generated report file, or None if error
    """
    logger = get_logger('open.buscar_tickets')

    try:
        log_operation_start(logger, "búsqueda de tickets", cedula=cedula, mostrar_todos=todos)

        # Crear instancia del TicketManager
        ticket_manager = TicketManager()

        # Consulta directa a la API para obtener tickets (Work Orders)
        logger.info(f"Buscando tickets (Work Orders) para cédula {cedula}")
        # URL para consultar work orders
        api_base_url = ticket_manager.api_base_url
        url_work_orders = f"{api_base_url}/arsys/v1/entry/WOI:WorkOrderInterface"
        
        # Parámetros de consulta para work orders - incluir todos los estados
        params_wo = {
            "q": f"'Direct Contact Corporate ID'=\"{cedula}\"",
            "limit": "1000"  # Aumentar límite para obtener más resultados
        }
        
        # Realizar la consulta para work orders
        response_wo = requests.get(url_work_orders, headers=ticket_manager.headers, params=params_wo)
        
        tickets = []
        if response_wo.status_code == 200:
            data_wo = response_wo.json()
            entries_wo = data_wo.get('entries', [])
            
            for entry in entries_wo:
                values = entry.get('values', {})
                
                # Mejorar el manejo de campos vacíos
                summary = values.get('Summary') or values.get('Short Description') or 'Sin resumen disponible'
                description = values.get('Detailed Description') or values.get('Description') or values.get('Notes') or 'Sin descripción disponible'
                customer = values.get('Customer') or values.get('Customer First Name', '') + ' ' + values.get('Customer Last Name', '') or values.get('Requestor') or 'No especificado'
                
                ticket_info = {
                    'request_id': values.get('Work Order ID'),
                    'summary': summary.strip(),
                    'status': values.get('Status'),
                    'detailed_description': description.strip(),
                    'assigned_to': values.get('Assigned To'),
                    'assignee_groups': values.get('Assignee Groups'),
                    'last_modified_date': values.get('Last Modified Date'),
                    'create_date': values.get('Create Date'),
                    'priority': values.get('Priority'),
                    'impact': values.get('Impact'),
                    'urgency': values.get('Urgency'),
                    'submitter': values.get('Submitter'),
                    'customer': customer.strip(),
                    'requestor_id': values.get('Requestor ID'),
                    'region': values.get('Region'),
                    'site_group': values.get('Site Group'),
                    'dwp_number': values.get('DWP_SRID') or values.get('Request ID') or 'N/A'
                }
                tickets.append(ticket_info)
        
        # Obtener notas para cada ticket
        for ticket in tickets:
            try:
                # Consultar notas del work order
                work_order_id = ticket.get('request_id')
                if work_order_id:
                    # URL para consultar notas de work orders
                    url_notes = f"{api_base_url}/arsys/v1/entry/WOI:WorkInfo"
                    params_notes = {
                        "q": f"'Work Order ID'=\"{work_order_id}\"",
                        "limit": "2"  # Las dos notas más recientes
                    }
                    
                    response_notes = requests.get(url_notes, headers=ticket_manager.headers, params=params_notes)
                    
                    if response_notes.status_code == 200:
                        notes_data = response_notes.json()
                        notes_entries = notes_data.get('entries', [])
                        
                        if notes_entries:
                            # Crear lista con las notas encontradas
                            notes_list = []
                            for i, entry in enumerate(notes_entries):
                                note_values = entry.get('values', {})
                                note_info = {
                                    'note_number': i + 1,
                                    'description': note_values.get('Summary', 'Sin resumen'),
                                    'detailed_description': note_values.get('Detailed Description', 'Sin descripción detallada'),
                                    'submitter': note_values.get('Submitter', 'N/A'),
                                    'last_modified_date': note_values.get('Last Modified Date', 'N/A')
                                }
                                notes_list.append(note_info)
                            
                            # Solo mantener last_note, eliminar notes
                            ticket['last_note'] = notes_list[0] if notes_list else None
                        else:
                            ticket['last_note'] = {
                                'description': 'Sin notas',
                                'detailed_description': 'No hay notas disponibles para este ticket'
                            }
                    else:
                        ticket['last_note'] = {
                            'description': 'Error al obtener notas',
                            'detailed_description': f'Error {response_notes.status_code}: {response_notes.text}'
                        }
                else:
                    ticket['last_note'] = {
                        'description': 'Sin ID de ticket',
                        'detailed_description': 'No se puede consultar notas sin ID de ticket'
                    }
            except Exception as e:
                ticket['last_note'] = {
                    'description': 'Error en consulta',
                    'detailed_description': f'Error al consultar notas: {str(e)}'
                }
        
        logger.info(f"Se encontraron {len(tickets)} work orders")

        # Consulta directa a la API para obtener incidentes
        logger.info(f"Buscando incidentes para cédula {cedula}")
        # URL para consultar incidentes
        url_incidents = f"{api_base_url}/arsys/v1/entry/HPD:IncidentInterface"
        
        # Parámetros de consulta para incidentes - incluir todos los estados
        params_inc = {
            "q": f"'Direct Contact Corporate ID'=\"{cedula}\"",
            "limit": "1000"  # Aumentar límite para obtener más resultados
        }
        
        # Realizar la consulta para incidentes
        response_inc = requests.get(url_incidents, headers=ticket_manager.headers, params=params_inc)
        
        incidentes = []
        if response_inc.status_code == 200:
            data_inc = response_inc.json()
            entries_inc = data_inc.get('entries', [])
            
            for entry in entries_inc:
                values = entry.get('values', {})
                
                # Mejorar el manejo de campos vacíos
                summary = values.get('Summary') or values.get('Short Description') or 'Sin resumen disponible'
                description = values.get('Detailed Description') or values.get('Description') or values.get('Notes') or 'Sin descripción disponible'
                customer = values.get('Customer') or values.get('Customer First Name', '') + ' ' + values.get('Customer Last Name', '') or values.get('Requestor') or 'No especificado'
                
                incidente_info = {
                    'incident_id': values.get('Incident Number'),
                    'summary': description.strip(),  # Intercambiado: ahora summary muestra detailed_description
                    'status': values.get('Status'),
                    'detailed_description': summary.strip(),  # Intercambiado: ahora detailed_description muestra summary
                    'assigned_to': values.get('Assigned To'),
                    'assignee_groups': values.get('Assignee Groups'),
                    'last_modified_date': values.get('Last Modified Date'),
                    'create_date': values.get('Create Date'),
                    'priority': values.get('Priority'),
                    'impact': values.get('Impact'),
                    'urgency': values.get('Urgency'),
                    'customer': customer.strip(),
                    'login_id': values.get('Requestor ID'),
                    'service_type': values.get('Service Type'),
                    'service': values.get('Service'),
                    'region': values.get('Region'),
                    'site_group': values.get('Site Group'),
                    'resolution': values.get('Resolution'),
                    'resolution_method': values.get('Resolution Method'),
                    'dwp_number': values.get('DWP_SRID') or values.get('Incident Number') or 'N/A'
                }
                incidentes.append(incidente_info)
        
        # Obtener notas para cada incidente
        for incidente in incidentes:
            try:
                # Consultar notas del incidente
                incident_id = incidente.get('incident_id')
                if incident_id:
                    # URL para consultar notas de incidentes
                    url_notes = f"{api_base_url}/arsys/v1/entry/HPD:WorkLog"
                    params_notes = {
                        "q": f"'Incident Number'=\"{incident_id}\" AND 'Work Log Type' = \"General Information\"",
                        "limit": "2"  # Las dos notas más recientes
                    }
                    
                    response_notes = requests.get(url_notes, headers=ticket_manager.headers, params=params_notes)
                    
                    if response_notes.status_code == 200:
                        notes_data = response_notes.json()
                        notes_entries = notes_data.get('entries', [])
                        
                        if notes_entries:
                            # Crear lista con las notas encontradas
                            notes_list = []
                            for i, entry in enumerate(notes_entries):
                                note_values = entry.get('values', {})
                                note_info = {
                                    'note_number': i + 1,
                                    'description': note_values.get('Summary', 'Sin resumen'),
                                    'detailed_description': note_values.get('Detailed Description', 'Sin descripción detallada'),
                                    'last_modified_date': note_values.get('Last Modified Date', 'N/A')
                                }
                                notes_list.append(note_info)
                            
                            # Solo mantener last_note, eliminar notes
                            incidente['last_note'] = notes_list[0] if notes_list else None
                        else:
                            incidente['last_note'] = {
                                'description': 'Sin notas',
                                'detailed_description': 'No hay notas disponibles para este incidente'
                            }
                    else:
                        incidente['last_note'] = {
                            'description': 'Error al obtener notas',
                            'detailed_description': f'Error {response_notes.status_code}: {response_notes.text}'
                        }
                else:
                    incidente['last_note'] = {
                        'description': 'Sin ID de incidente',
                        'detailed_description': 'No se puede consultar notas sin ID de incidente'
                    }
            except Exception as e:
                incidente['last_note'] = {
                    'description': 'Error en consulta',
                    'detailed_description': f'Error al consultar notas: {str(e)}'
                }
        
        print(f"Se encontraron {len(incidentes)} incidentes")
        
        if not tickets and not incidentes:
            print(f"\n{Fore.YELLOW}No se encontraron tickets ni incidentes para la cédula {cedula}{Style.RESET_ALL}")
            return None
        
        # Obtener fecha y hora actuales
        fecha_consulta = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # Crear estructura para el reporte unificado
        reporte = {
            "tickets": {
                "cedula": cedula,
                "fecha_consulta": fecha_consulta,
                "total_tickets": len(tickets),
                "tickets_procesados": len(tickets),
                "tickets": tickets
            },
            "incidents": {
                "login_id": cedula,
                "fecha_consulta": fecha_consulta,
                "total_incidentes": len(incidentes),
                "incidentes_procesados": len(incidentes),
                "incidentes": incidentes
            }
        }
        
        # Guardar el reporte como JSON
        reporte_file = os.path.join(os.path.dirname(__file__), "reporte_unificado.json")
        with open(reporte_file, 'w', encoding='utf-8') as f:
            json.dump(reporte, f, indent=2, ensure_ascii=False)
        
        print(f"\n{Fore.GREEN}Reporte unificado generado correctamente: {reporte_file}{Style.RESET_ALL}")
        
        return reporte_file
    except Exception as e:
        print(f"\n{Fore.RED}Error al buscar tickets: {str(e)}{Style.RESET_ALL}")
        return None


def _display_tickets_section(data: Dict[str, Any], displayer, logger):
    """Display tickets section of the report."""
    if "tickets" not in data or not isinstance(data["tickets"], dict):
        return

    tickets_info = data["tickets"]
    cedula = tickets_info.get("cedula", "N/A")
    fecha_consulta = tickets_info.get("fecha_consulta", "N/A")
    total_tickets = tickets_info.get("total_tickets", 0)
    tickets_procesados = tickets_info.get("tickets_procesados", 0)

    displayer.display_section_header("INFORMACIÓN DE TICKETS")
    displayer.display_info_line("Cédula", cedula)
    displayer.display_info_line("Fecha de consulta", fecha_consulta)
    displayer.display_info_line("Total de tickets", total_tickets)
    displayer.display_info_line("Tickets procesados", tickets_procesados)

    # Mostrar tickets
    tickets = tickets_info.get("tickets", [])
    if tickets:
        displayer.display_section_header(f"TICKETS ({len(tickets)})")

        priority_fields = ["request_id", "summary", "status", "priority", "dwp_number", "detailed_description"]

        for i, ticket in enumerate(tickets):
            request_id = ticket.get('request_id', 'Sin ID')
            displayer.display_item_header("Ticket", i+1, request_id)

            # Create and display table
            table = displayer.create_item_table(ticket, priority_fields)
            displayer.display_table(table)

            # Display last note if exists
            if 'last_note' in ticket and ticket['last_note']:
                displayer.display_note_header()
                note_table = displayer.create_note_table(ticket['last_note'])
                displayer.display_table(note_table)
    else:
        displayer.display_warning("No se encontraron tickets.")


def _display_incidents_section(data: Dict[str, Any], displayer, logger, mostrar_todos: bool = False):
    """Display incidents section of the report."""
    if "incidents" not in data or not isinstance(data["incidents"], dict):
        return

    incidents_info = data["incidents"]
    login_id = incidents_info.get("login_id", "N/A")
    fecha_consulta = incidents_info.get("fecha_consulta", "N/A")
    total_incidentes = incidents_info.get("total_incidentes", 0)
    incidentes_procesados = incidents_info.get("incidentes_procesados", 0)

    displayer.display_section_header("INFORMACIÓN DE INCIDENTES")
    displayer.display_info_line("Login ID", login_id)
    displayer.display_info_line("Fecha de consulta", fecha_consulta)
    displayer.display_info_line("Total de incidentes", total_incidentes)
    displayer.display_info_line("Incidentes procesados", incidentes_procesados)

    # Mostrar incidentes
    incidentes = incidents_info.get("incidentes", [])
    if incidentes:
        displayer.display_section_header(f"INCIDENTES ({len(incidentes)})")

        # Show all incidents or limit to 5
        max_incidentes = len(incidentes) if mostrar_todos else min(5, len(incidentes))
        priority_fields = ["incident_id", "summary", "status", "priority", "dwp_number", "detailed_description"]

        for i, incidente in enumerate(incidentes[:max_incidentes]):
            incident_id = incidente.get('incident_id', 'Sin ID')
            displayer.display_item_header("Incidente", i+1, incident_id)

            # Create and display table
            table = displayer.create_item_table(incidente, priority_fields)
            displayer.display_table(table)

            # Display last note if exists
            if 'last_note' in incidente and incidente['last_note']:
                displayer.display_note_header()
                note_table = displayer.create_note_table(incidente['last_note'])
                displayer.display_table(note_table)

        # Show message if not all incidents are displayed
        if not mostrar_todos and len(incidentes) > 5:
            displayer.display_warning(f"Mostrando {max_incidentes} de {len(incidentes)} incidentes. Use '--todos' para ver todos.")
    else:
        displayer.display_warning("No se encontraron incidentes.")


if __name__ == "__main__":
    # Verificar argumentos
    if len(sys.argv) > 1:
        if sys.argv[1] == '--simple':
            # Modo simple: solo procesar JSON existente para enviar por correo
            json_file = os.path.join(os.path.dirname(__file__), "test_report.json")
            from test_mail import crear_mensaje_desde_json
            mensaje_html = crear_mensaje_desde_json(json_file)
            print("Mensaje HTML generado correctamente para envío por correo.")
        else:
            # Buscar tickets por cédula
            cedula = sys.argv[1]
            mostrar_todos = '--todos' in sys.argv
            
            # Ejecutar búsqueda y generar el reporte
            reporte_file = buscar_tickets_por_cedula(cedula, mostrar_todos)
            
            if reporte_file:
                # Mostrar el reporte en la consola - CORRECCIÓN: pasar mostrar_todos
                mostrar_reporte_unificado(reporte_file, mostrar_todos)
    else:
        print(f"\n{Fore.YELLOW}Uso: python {sys.argv[0]} <cedula> [--todos]{Style.RESET_ALL}")
        print(f"  --todos: Muestra todos los incidentes (por defecto solo muestra los 5 primeros)")
        print(f"  --simple: Procesa un JSON existente para enviar por correo")
