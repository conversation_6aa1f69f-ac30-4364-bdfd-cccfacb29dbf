#!/usr/bin/env python
import argparse
import os
import sys
import logging
import json
import time
import shutil
from datetime import datetime

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(os.path.join(os.path.dirname(__file__), 'procesar_todo.log'))
    ]
)
logger = logging.getLogger('procesar_todo')

# Importar módulos necesarios
try:
    from mail import Mail
    import enviar_reporte
except ImportError as e:
    logger.error(f"Error al importar módulos: {str(e)}")
    sys.exit(1)

def procesar_y_enviar(cedula, destinatario=None):
    """Procesa el reporte unificado existente y envía por correo"""
    try:
        logger.info(f"Procesando datos para cédula: {cedula}")
        
        # Vamos a utilizar directamente el archivo de reporte unificado
        # ya que contiene los tickets y no necesitamos modificarlo
        reporte_file = os.path.join(os.path.dirname(__file__), "reporte_unificado.json")
        
        # Verificar si el archivo existe
        if not os.path.exists(reporte_file):
            logger.error(f"El archivo de reporte no existe: {reporte_file}")
            return False
        
        # Verificar que el reporte contiene tickets
        try:
            with open(reporte_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            tickets_count = 0
            if "tickets" in data and isinstance(data["tickets"], dict) and "tickets" in data["tickets"]:
                tickets_count = len(data["tickets"]["tickets"])
            
            # Informar sobre los tickets encontrados
            logger.info(f"Se encontraron {tickets_count} tickets en el reporte.")
            
            if tickets_count == 0:
                logger.warning("El reporte no contiene tickets. Puede que el correo llegue vacío.")
        except Exception as e:
            logger.error(f"Error al verificar tickets en el reporte: {str(e)}")
        
        # No necesitamos modificar el archivo, lo usamos directamente
        
        # Mostrar el reporte en la consola
        try:
            # Importar open.py sin conflicto con la función integrada open()
            import importlib.util
            spec = importlib.util.spec_from_file_location("open_module", "open.py")
            if spec and spec.loader:
                open_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(open_module)
                
                # Si tiene la función para mostrar el reporte, llamarla
                if hasattr(open_module, 'mostrar_reporte_unificado'):
                    open_module.mostrar_reporte_unificado(reporte_file)
                else:
                    # Si no, ejecutaremos el script principal directamente
                    logger.info("Ejecutando open.py independientemente...")
                    import subprocess
                    subprocess.run([sys.executable, 'open.py', reporte_file], check=False)
            else:
                # Si no se puede cargar el módulo, ejecutarlo como proceso
                logger.info("Ejecutando open.py como proceso independiente")
                import subprocess
                subprocess.run([sys.executable, 'open.py', reporte_file], check=False)
            logger.info("Visualización en consola completada")
        except Exception as e:
            logger.error(f"Error al mostrar en consola: {str(e)}")
            logger.info("Continuando con el proceso...")
        
        # Enviar el reporte por correo
        if destinatario is None:
            # Obtener destinatario por defecto del archivo .env
            from dotenv import load_dotenv
            load_dotenv(os.path.join(os.path.dirname(__file__), '.env'))
            destinatario = os.getenv('EMAIL_TO', '<EMAIL>')  # Valor predeterminado de respaldo
        
        try:
            # Usar la función enviar_reporte del módulo importado
            logger.info(f"Enviando reporte a {destinatario} usando enviar_reporte.py...")
            enviado = enviar_reporte.enviar_reporte(reporte_file, destinatario)
            if enviado:
                logger.info(f"Reporte enviado correctamente a {destinatario}")
                return True
            else:
                raise Exception("No se confirmó el envío")
        except Exception as e1:
            logger.error(f"Error al usar enviar_reporte.py: {str(e1)}")
            
            # Plan alternativo: enviar correo directamente con Mail
            try:
                logger.info("Intentando enviar correo utilizando Mail directamente...")
                
                # Leer el contenido del JSON para obtener información
                with open(reporte_file, 'r', encoding='utf-8') as f:
                    report_data = json.load(f)
                
                # Extraer información de tickets
                tickets_info = report_data.get("tickets", {})
                cedula_actual = tickets_info.get("cedula", cedula)
                total_tickets = tickets_info.get("total_tickets", 0)
                tickets_procesados = tickets_info.get("tickets_procesados", 0)
                tickets = tickets_info.get("tickets", [])
                
                # Crear un mensaje HTML más completo
                mensaje = f"<html><body>"  
                mensaje += f"<h1>Reporte Unificado de Tickets e Incidentes</h1>"
                mensaje += f"<p><strong>Cédula:</strong> {cedula_actual}</p>"
                mensaje += f"<p><strong>Fecha:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>"
                mensaje += f"<p><strong>Total de tickets:</strong> {total_tickets}</p>"
                
                # Si hay tickets, mostrar información básica
                if tickets:
                    mensaje += f"<h2>Tickets encontrados ({len(tickets)}):</h2>"
                    mensaje += f"<ul>"
                    for ticket in tickets:
                        request_id = ticket.get("request_id", "Sin ID")
                        summary = ticket.get("summary", "Sin descripción")
                        status = ticket.get("status", "Sin estado")
                        mensaje += f"<li><strong>{request_id}</strong>: {summary} - <em>Estado: {status}</em></li>"
                    mensaje += f"</ul>"
                    mensaje += f"<p>Para ver el reporte completo, revise su bandeja de entrada o consulte la consola.</p>"
                else:
                    mensaje += f"<p>No se encontraron tickets para esta cédula.</p>"
                
                mensaje += f"<p>Este reporte ha sido generado automáticamente.</p>"
                mensaje += f"</body></html>"
                
                mail = Mail(destinatario)
                mail.send(
                    subject=f"Reporte Unificado - Cédula {cedula_actual}",
                    message=mensaje
                )
                logger.info(f"Reporte alternativo enviado correctamente a {destinatario}")
                return True
            except Exception as e2:
                logger.error(f"Error al enviar correo directamente: {str(e2)}")
                return False
    except Exception as e:
        logger.error(f"Error en el procesamiento: {str(e)}")
        return False

def main():
    """Función principal"""
    parser = argparse.ArgumentParser(description='Procesar y enviar reporte para una cédula')
    parser.add_argument('cedula', help='Número de cédula a procesar')
    parser.add_argument('--destinatario', '-d', help='Correo del destinatario (opcional)')
    
    args = parser.parse_args()
    
    # Ejecutar el procesamiento y enviar correo
    resultado = procesar_y_enviar(args.cedula, args.destinatario)
    
    # Salir con código de error si hubo problemas
    if not resultado:
        logger.error("El proceso terminó con errores.")
        sys.exit(1)
    else:
        logger.info(f"Proceso completado exitosamente para la cédula {args.cedula}")

if __name__ == "__main__":
    main()
