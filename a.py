#!/usr/bin/python3.6
# -*- coding: utf-8 -*-
"""
Módulo para la gestión de tickets en BMC Remedy.
Este módulo proporciona una interfaz simplificada para crear y buscar tickets.
"""

import os
import json
import requests
from dotenv import load_dotenv
import os.path
import base64
import mimetypes

# Cargar variables de entorno desde .env
load_dotenv()

class JWTAuthenticator:
    """Clase encargada de la autenticación JWT."""

    def __init__(self, login_url=None):
        # Cargar variables de entorno
        load_dotenv()
        self.login_url = login_url or os.getenv('LOGIN_URL')
        self.username = os.getenv('USERNAME_', 'Integracion.VoiceBot')
        self.password = os.getenv('PASSWORD', '$ur@2025*')
        self.headers = {'Content-Type': 'application/x-www-form-urlencoded'}
        self.debug = os.getenv('DEBUG', 'False').lower() == 'true'

    def get_token(self):
        """
        Obtiene el token JWT haciendo un POST con usuario/contraseña.
        :return: Token JWT o None en caso de error
        """
        payload = {
            'username': self.username,
            'password': self.password
        }

        try:


            response = requests.post(self.login_url, headers=self.headers, data=payload)
            response.raise_for_status()



            return response.text
        except requests.exceptions.RequestException as e:
            if self.debug:
                print(f"Error en la autenticación: {e}")
            return None


class TicketManager:
    """
    Clase para la gestión de tickets en BMC Remedy.
    Proporciona métodos para crear y buscar tickets.
    """

    def __init__(self):
        # Cargar variables de entorno
        load_dotenv()

        # Configurar URL base desde variables de entorno
        self.api_base_url = os.getenv('API_BASE_URL', 'https://surasoporteti-qa-restapi.onbmc.com/api')

        # Obtener token JWT
        login_url = f"{self.api_base_url}/jwt/login"
        self.authenticator = JWTAuthenticator(login_url)
        self.token = self.authenticator.get_token()

        if not self.token:
            raise Exception("No se pudo obtener el token JWT. Verifique las credenciales en el archivo .env")

        # Configurar headers con el token
        self.headers = {
            'Authorization': f'AR-JWT {self.token}',
            'Content-Type': 'application/json',
            'Cookie': f'AR-JWT={self.token}'
        }

    def _get_user_by_cedula(self, cedula):
        """
        Consulta un usuario en CTM:People usando 'Corporate ID' = cedula
        Retorna el primer registro (dict) con todos los campos o None si no se encuentra.
        """
        # URL para consultar usuario por cédula
        url = f"{self.api_base_url}/arsys/v1/entry/CTM:People/?q='Corporate ID'=\"{cedula}\""

        try:

            response = requests.get(url, headers=self.headers)

            if response.status_code == 200:
                data = response.json()
                entries = data.get('entries', [])

                if entries:
                    # Tomar el primer registro encontrado
                    user_data = entries[0].get('values', {})
                    return user_data
                else:
                    return self._create_generic_user(cedula)
            else:
                return self._create_generic_user(cedula)

        except requests.exceptions.RequestException as e:
            return self._create_generic_user(cedula)

    def _create_generic_user(self, cedula):
        """
        Crea un diccionario con datos genéricos para un usuario
        cuando no se encuentra en el sistema.
        """
        return {
            'First Name': 'Usuario',
            'Last Name': f'Cédula {cedula}',
            'Corporate ID': cedula,
            'Internet E-mail': f'usuario.{cedula}@example.com',
            'Company': 'SURA',
            'Organization': 'SURA',
            'Department': 'PENDIENTE',
            'Site': 'ADMINISTRATIVA',
            'Region': 'Antioquia',
            'Site Group': 'Antioquia',
            'Phone Number': '###'
        }

    def create_ticket(self, cedula, descripcion, telefono=None, archivo_adjunto=None):
        """
        Crea un ticket en BMC Remedy para el usuario con la cédula especificada.

        Args:
            cedula (str): Número de cédula del usuario
            descripcion (str): Descripción del incidente
            telefono (str, optional): Número de teléfono del usuario
            archivo_adjunto (str, optional): Ruta al archivo a adjuntar

        Returns:
            tuple: (incident_number, dwp_number) donde incident_number es el número del incidente creado y 
                  dwp_number es el número de petición DWP, o (None, None) en caso de error
        """
        # Paso 1: Obtener datos del usuario
        user_data = self._get_user_by_cedula(cedula)

        # Si se proporciona un teléfono, actualizar el dato del usuario
        if telefono:
            user_data['Phone Number'] = telefono

        # Paso 2: Crear el incidente

        # URL para crear incidentes
        url = f"{self.api_base_url}/arsys/v1/entry/HPD:IncidentInterface_Create"

        # Obtener datos del usuario
        first_name = user_data.get('First Name', 'Usuario')
        last_name = user_data.get('Last Name', f'Cédula {cedula}')

        # Preparar payload para crear el incidente
        payload = {
            "values": {
                "Company": "SURA",
                "First_Name": first_name,
                "Last_Name": last_name,
                "Direct Contact First Name": first_name,
                "Direct Contact Last Name": last_name,
                "Description": descripcion,
                "Detailed_Decription": f"Caso creado automáticamente para el usuario {first_name} {last_name} con cédula {cedula}. Descripción: {descripcion}. telefono:{telefono} ",
                "Impact": "3-Moderate/Limited",
                "Urgency": "3-Medium",
                "Status": "Assigned",
                "HPD_CI": "Impresora",
                "Reported Source": "Voicebot",
                "Assigned Support Company": "SURA",
                "Assigned Support Organization": "Soporte BMC",
                "Assigned Group": "Protocolo de Atención Incidentes BMC",
                "Assignee": "Camilo Andres Martinez Sanchez", #eliminar campo 
                "Service_Type": "Infrastructure Event",
                "Categorization Tier 1": "SERVICIOS DE TECNOLOGIA",
                "Categorization Tier 2": "GESTION ITSM",
                "Categorization Tier 3": "ERROR DE APROBACIONES",
                "Product Categorization Tier 1": "Aplicaciones Transversales",
                "Product Categorization Tier 2": "CA Catalogo de Servicios",
                "Product Categorization Tier 3": "CA Catalogo de Servicios",
                "z1D_Action": "CREATE"
            }
        }

        try:
            response = requests.post(url, json=payload, headers=self.headers)


            if response.status_code == 201:

                # Obtener el ID del incidente creado desde la cabecera Location
                location = response.headers.get('Location', '')
                incident_id = location.split('/')[-1] if location else None

                if incident_id:

                    # Consultar los detalles del caso para obtener el Incident Number
                    incident_number = self._get_incident_number(incident_id)

                    if incident_number:
                        # Obtener todos los detalles del incidente
                        incident_details = self._get_incident_details(incident_number)

                        # Obtener el número de petición DWP
                        dwp_number = self.get_dwp_request_number(incident_number)

                        if archivo_adjunto:
                            # Si se proporciona un archivo adjunto, adjuntarlo al incidente
                            if os.path.exists(archivo_adjunto):
                                self._attach_file_to_incident(incident_number, archivo_adjunto)
                            else:
                                return None, None

                        return incident_number, dwp_number
                    else:
                        return None, None
                else:
                    return None, None
            else:
                return None, None

        except requests.exceptions.RequestException as e:
            return None, None

    def _get_incident_number(self, incident_id):
        """
        Obtiene el número de incidente a partir del ID interno.

        Args:
            incident_id (str): ID interno del incidente

        Returns:
            str: Número del incidente o None en caso de error
        """
        # Consultar directamente en HPD:IncidentInterface_Create
        url = f"{self.api_base_url}/arsys/v1/entry/HPD:IncidentInterface_Create/{incident_id}"

        try:
            response = requests.get(url, headers=self.headers)

            if response.status_code == 200:
                incident_details = response.json()

                if 'values' in incident_details and 'Incident Number' in incident_details['values']:
                    incident_number = incident_details['values']['Incident Number']
                    return incident_number
                else:
                    return None
            else:
                return None

        except requests.exceptions.RequestException as e:
            return None

    def _get_incident_details(self, incident_number):
        """
        Obtiene todos los detalles de un incidente.

        Args:
            incident_number (str): Número del incidente

        Returns:
            dict: Detalles completos del incidente o None en caso de error
        """
        # Lista de entidades a consultar
        entities = [
            "HPD:Help Desk",
            "HPD:IncidentInterface",
            "SRM:RequestInterface"
        ]
        
        results = {}
        
        for entity in entities:
            # URL para consultar el incidente
            url = f"{self.api_base_url}/arsys/v1/entry/{entity}"
            
            # Parámetros de consulta
            params = {
                "q": f"'Incident Number'=\"{incident_number}\""
            }
            
            try:
                response = requests.get(url, headers=self.headers, params=params)
                
                if response.status_code == 200:
                    data = response.json()
                    entries = data.get('entries', [])
                    
                    if entries:
                        results[entity] = entries
                
            except requests.exceptions.RequestException as e:
                pass
        
        return results

    def get_dwp_request_number(self, incident_number):
        """
        Obtiene el número de petición DWP relacionada con un incidente.

        Args:
            incident_number (str): Número del incidente (formato INC000000XXXXX)

        Returns:
            str: Número de petición DWP o None si no se encuentra
        """
        # URL para consultar el incidente
        url = f"{self.api_base_url}/arsys/v1/entry/HPD:Help Desk"
        
        # Parámetros de consulta
        params = {
            "q": f"'Incident Number'=\"{incident_number}\"",
            "fields": "values(Incident Number,DWP_SRID)"
        }
        
        try:
            response = requests.get(url, headers=self.headers, params=params)
            
            if response.status_code == 200:
                data = response.json()
                entries = data.get('entries', [])
                
                if entries and 'values' in entries[0]:
                    values = entries[0]['values']
                    dwp_request_number = values.get('DWP_SRID')
                    return dwp_request_number
            
            return None
            
        except requests.exceptions.RequestException as e:
            return None

    def _attach_file_to_incident(self, incident_number, file_path):
        """
        Adjunta un archivo a un WorkLog del incidente.

        Args:
            incident_number (str): Número del incidente
            file_path (str): Ruta al archivo a adjuntar

        Returns:
            bool: True si se adjuntó correctamente, False en caso contrario
        """
        if not os.path.exists(file_path):
            return False

        # URL para adjuntar archivos
        url = f"{self.api_base_url}/arsys/v1/entry/HPD:WorkLog"

        # Obtener el nombre del archivo sin la ruta
        file_name = os.path.basename(file_path)

        # Preparar el payload para la API con todos los campos requeridos
        entry_data = {
            "values": {
                "Incident Number": incident_number,
                "Status": "Enabled",  # Valor requerido para el campo Status
                "Work Log Type": "General Information",
                "View Access": "Public",
                "Secure Work Log": "No",
                "Description": f"Archivo adjuntado automáticamente: {file_name}",
                "Detailed Description": f"Archivo adjuntado automáticamente al caso {incident_number}",
                "z2AF Work Log01": file_name,
                "Communication Source": "Web",  # Campo requerido
                "Work Log Date": self._get_current_date_time(),  # Fecha actual en formato adecuado
                "Work Log Submit Date": self._get_current_date_time()  # Fecha actual en formato adecuado
            }
        }

        try:
            # Crear un archivo multipart/form-data
            with open(file_path, 'rb') as file_data:
                files = {
                    'entry': (None, json.dumps(entry_data), 'application/json'),
                    'attach-z2AF Work Log01': (file_name, file_data, 'application/octet-stream')
                }

                # Enviar la solicitud con los headers de autenticación
                headers = {
                    'Authorization': f'AR-JWT {self.token}'
                }

                response = requests.post(url, headers=headers, files=files)

            if response.status_code in [200, 201]:
                return True
            else:
                return incident_number

        except Exception as e:
            return False

    def _get_current_date_time(self):
        """
        Devuelve la fecha y hora actual en formato ISO 8601

        Returns:
            str: Fecha y hora actual en formato ISO 8601
        """
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ")

    def search_tickets(self, cedula):
        """
        Busca tickets asociados a un usuario por su cédula.

        Args:
            cedula (str): Número de cédula del usuario

        Returns:
            list: Lista de diccionarios con información de los tickets encontrados
        """

        # URL para consultar incidentes
        url = f"{self.api_base_url}/arsys/v1/entry/HPD:IncidentInterface"

        # Parámetros de consulta - Eliminamos el parámetro sort que causa problemas
        params = {
            "q": f"'Direct Contact Corporate ID'=\"{cedula}\""
        }

        try:
            response = requests.get(url, headers=self.headers, params=params)

            if response.status_code == 200:
                data = response.json()
                entries = data.get('entries', [])

                if entries:

                    # Extraer información relevante de cada ticket
                    tickets = []
                    for entry in entries:
                        values = entry.get('values', {})
                        ticket_info = {
                            'incident_number': values.get('Incident Number'),
                            'description': values.get('Description'),
                            'status': values.get('Status'),
                            'submit_date': values.get('Submit Date'),
                            'assignee': values.get('Assignee')
                        }
                        tickets.append(ticket_info)

                    # Ordenar los tickets por fecha de creación (más recientes primero)
                    # Ya que no podemos usar el parámetro sort en la API
                    tickets.sort(key=lambda x: x.get('submit_date', ''), reverse=True)

                    return tickets
                else:
                    return []
            else:
                return []

        except requests.exceptions.RequestException as e:
            return []

    def attach_file_to_ticket(self, incident_number, file_path=None):
        """
        Adjunta un archivo específico a un ticket existente por su número de incidente.
        Si no se proporciona una ruta de archivo, se intentará adjuntar 'archivo_prueba.txt'

        Args:
            incident_number (str): Número del incidente (formato INC000000XXXXX)
            file_path (str, optional): Ruta al archivo a adjuntar. Si es None, se usará 'archivo_prueba.txt'

        Returns:
            tuple: (incident_number, dwp_number) donde incident_number es el número del incidente y 
                  dwp_number es el número de petición DWP, o (None, None) en caso de error
        """
        # Si no se proporciona un archivo, usar archivo_prueba.txt
        if not file_path:
            file_path = 'archivo_prueba.txt'

        # Verificar que el incidente existe
        if not self._verify_incident_exists(incident_number):
            return None, None

        # Adjuntar el archivo
        if self._attach_file_to_incident(incident_number, file_path):
            # Obtener el número de petición DWP
            dwp_number = self.get_dwp_request_number(incident_number)
            return incident_number, dwp_number
        
        return None, None

    def _verify_incident_exists(self, incident_number):
        """
        Verifica que un incidente existe en el sistema.

        Args:
            incident_number (str): Número del incidente a verificar

        Returns:
            bool: True si el incidente existe, False en caso contrario
        """
        # URL para consultar incidentes
        url = f"{self.api_base_url}/arsys/v1/entry/HPD:IncidentInterface"

        # Parámetros de consulta
        params = {
            "q": f"'Incident Number'=\"{incident_number}\""
        }

        try:
            response = requests.get(url, headers=self.headers, params=params)

            if response.status_code == 200:
                data = response.json()
                entries = data.get('entries', [])

                if entries:
                    return True
                else:
                    return False
            else:
                return False

        except requests.exceptions.RequestException as e:

            return False


# Función de prueba
if __name__ == "__main__":
    # Ejemplo de uso
    ticket_manager = TicketManager()

    # Crear un ticket
    cedula = "1030587399"
    descripcion = "Prueba de creación de ticket desde TicketManager"

    # Crear el ticket y mostrar todos los detalles
    ticket_number, dwp_number = ticket_manager.create_ticket(cedula, descripcion)
    
    # Adjuntar archivo al ticket
    if ticket_number:
        ticket_number, dwp_number = ticket_manager.attach_file_to_ticket(ticket_number, './a.txt')
