2025-06-26 11:00:21,741 - main - [32mINFO[0m - None:0 - Starting aplicación principal | Data: {'operation': 'aplicación principal', 'cedula': '1152213619', 'destinatario': '<EMAIL>', 'todos': True, 'version': '1.0.0', 'debug_mode': False, 'silent_mode': False}
2025-06-26 11:00:21,741 - main.flujo_completo - [32mINFO[0m - None:0 - Starting flujo completo | Data: {'operation': 'flujo completo', 'cedula': '1152213619', 'destinatario': '<EMAIL>', 'todos': True} | Data: {'operation': 'flujo completo', 'cedula': '1152213619', 'destinatario': '<EMAIL>', 'todos': True}
2025-06-26 11:00:21,742 - main.flujo_completo - [32mINFO[0m - None:0 - Starting búsqueda de casos | Data: {'operation': 'búsqueda de casos', 'cedula': '1152213619', 'mostrar_todos': True} | Data: {'operation': 'búsqueda de casos', 'cedula': '1152213619', 'mostrar_todos': True}
2025-06-26 11:00:21,743 - main.flujo_completo - [32mINFO[0m - _ejecutar_busqueda_casos:86 - Ejecutando comando: C:\Users\<USER>\AppData\Local\anaconda3\python.exe open.py 1152213619 --todos
2025-06-26 11:00:29,970 - open.buscar_tickets - [32mINFO[0m - None:0 - Starting búsqueda de tickets | Data: {'operation': 'búsqueda de tickets', 'cedula': '1152213619', 'mostrar_todos': True} | Data: {'operation': 'búsqueda de tickets', 'cedula': '1152213619', 'mostrar_todos': True}
2025-06-26 11:00:30,620 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:78 - Buscando tickets (Work Orders) para cédula 1152213619
2025-06-26 11:00:31,096 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:181 - Se encontraron 0 work orders
2025-06-26 11:00:31,096 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:184 - Buscando incidentes para cédula 1152213619
2025-06-26 11:00:34,566 - open.mostrar_reporte - [32mINFO[0m - None:0 - Starting mostrar reporte | Data: {'operation': 'mostrar reporte', 'archivo': 'C:\\Users\\<USER>\\Documents\\voice - copia\\bmc-remedy-container\\reporte_unificado.json', 'mostrar_todos': True} | Data: {'operation': 'mostrar reporte', 'archivo': 'C:\\Users\\<USER>\\Documents\\voice - copia\\bmc-remedy-container\\reporte_unificado.json', 'mostrar_todos': True}
2025-06-26 11:00:34,568 - display - [32mINFO[0m - display_section_header:94 - INFORMACIÓN DE TICKETS
2025-06-26 11:00:34,568 - display - [32mINFO[0m - display_info_line:109 - Cédula: 1152213619
2025-06-26 11:00:34,568 - display - [32mINFO[0m - display_info_line:109 - Fecha de consulta: 2025-06-26 11:00:34
2025-06-26 11:00:34,568 - display - [32mINFO[0m - display_info_line:109 - Total de tickets: 0
2025-06-26 11:00:34,569 - display - [32mINFO[0m - display_info_line:109 - Tickets procesados: 0
2025-06-26 11:00:34,569 - display - [33mWARNING[0m - display_warning:174 - No se encontraron tickets.
2025-06-26 11:00:34,569 - display - [32mINFO[0m - display_section_header:94 - INFORMACIÓN DE INCIDENTES
2025-06-26 11:00:34,570 - display - [32mINFO[0m - display_info_line:109 - Login ID: 1152213619
2025-06-26 11:00:34,570 - display - [32mINFO[0m - display_info_line:109 - Fecha de consulta: 2025-06-26 11:00:34
2025-06-26 11:00:34,570 - display - [32mINFO[0m - display_info_line:109 - Total de incidentes: 6
2025-06-26 11:00:34,571 - display - [32mINFO[0m - display_info_line:109 - Incidentes procesados: 6
2025-06-26 11:00:34,571 - display - [32mINFO[0m - display_section_header:94 - INCIDENTES (6)
2025-06-26 11:00:34,571 - display - [32mINFO[0m - display_item_header:129 - Incidente #1: INC000000002266
2025-06-26 11:00:34,603 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:00:34,604 - display - [32mINFO[0m - display_item_header:129 - Incidente #2: INC000000015309
2025-06-26 11:00:34,605 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:00:34,605 - display - [32mINFO[0m - display_item_header:129 - Incidente #3: INC000000015311
2025-06-26 11:00:34,606 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:00:34,607 - display - [32mINFO[0m - display_item_header:129 - Incidente #4: INC000000016368
2025-06-26 11:00:34,608 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:00:34,609 - display - [32mINFO[0m - display_item_header:129 - Incidente #5: INC000000032662
2025-06-26 11:00:34,610 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:00:34,611 - display - [32mINFO[0m - display_item_header:129 - Incidente #6: INC000000056308
2025-06-26 11:00:34,611 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:00:34,612 - open.mostrar_reporte - [32mINFO[0m - None:0 - Successfully completed mostrar reporte | Data: {'operation': 'mostrar reporte', 'archivo': 'C:\\Users\\<USER>\\Documents\\voice - copia\\bmc-remedy-container\\reporte_unificado.json'} | Data: {'operation': 'mostrar reporte', 'archivo': 'C:\\Users\\<USER>\\Documents\\voice - copia\\bmc-remedy-container\\reporte_unificado.json'}
2025-06-26 11:00:34,697 - main.flujo_completo - [32mINFO[0m - None:0 - Successfully completed búsqueda de casos | Data: {'operation': 'búsqueda de casos', 'tickets_encontrados': 0, 'incidentes_encontrados': 6} | Data: {'operation': 'búsqueda de casos', 'tickets_encontrados': 0, 'incidentes_encontrados': 6}
2025-06-26 11:00:34,697 - main.flujo_completo - [32mINFO[0m - None:0 - Starting envío de reporte | Data: {'operation': 'envío de reporte', 'cedula': '1152213619', 'destinatario': '<EMAIL>'} | Data: {'operation': 'envío de reporte', 'cedula': '1152213619', 'destinatario': '<EMAIL>'}
2025-06-26 11:00:34,698 - main.flujo_completo - [32mINFO[0m - _ejecutar_envio_reporte:141 - Ejecutando comando: C:\Users\<USER>\AppData\Local\anaconda3\python.exe procesar_todo.py 1152213619 --destinatario <EMAIL>
2025-06-26 11:00:38,575 - open.mostrar_reporte - [32mINFO[0m - None:0 - Starting mostrar reporte | Data: {'operation': 'mostrar reporte', 'archivo': 'C:\\Users\\<USER>\\Documents\\voice - copia\\bmc-remedy-container\\reporte_unificado.json', 'mostrar_todos': False} | Data: {'operation': 'mostrar reporte', 'archivo': 'C:\\Users\\<USER>\\Documents\\voice - copia\\bmc-remedy-container\\reporte_unificado.json', 'mostrar_todos': False}
2025-06-26 11:00:38,576 - display - [32mINFO[0m - display_section_header:94 - INFORMACIÓN DE TICKETS
2025-06-26 11:00:38,576 - display - [32mINFO[0m - display_info_line:109 - Cédula: 1152213619
2025-06-26 11:00:38,577 - display - [32mINFO[0m - display_info_line:109 - Fecha de consulta: 2025-06-26 11:00:34
2025-06-26 11:00:38,577 - display - [32mINFO[0m - display_info_line:109 - Total de tickets: 0
2025-06-26 11:00:38,577 - display - [32mINFO[0m - display_info_line:109 - Tickets procesados: 0
2025-06-26 11:00:38,577 - display - [33mWARNING[0m - display_warning:174 - No se encontraron tickets.
2025-06-26 11:00:38,577 - display - [32mINFO[0m - display_section_header:94 - INFORMACIÓN DE INCIDENTES
2025-06-26 11:00:38,577 - display - [32mINFO[0m - display_info_line:109 - Login ID: 1152213619
2025-06-26 11:00:38,577 - display - [32mINFO[0m - display_info_line:109 - Fecha de consulta: 2025-06-26 11:00:34
2025-06-26 11:00:38,577 - display - [32mINFO[0m - display_info_line:109 - Total de incidentes: 6
2025-06-26 11:00:38,577 - display - [32mINFO[0m - display_info_line:109 - Incidentes procesados: 6
2025-06-26 11:00:38,578 - display - [32mINFO[0m - display_section_header:94 - INCIDENTES (6)
2025-06-26 11:00:38,578 - display - [32mINFO[0m - display_item_header:129 - Incidente #1: INC000000002266
2025-06-26 11:00:38,590 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:00:38,591 - display - [32mINFO[0m - display_item_header:129 - Incidente #2: INC000000015309
2025-06-26 11:00:38,592 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:00:38,592 - display - [32mINFO[0m - display_item_header:129 - Incidente #3: INC000000015311
2025-06-26 11:00:38,592 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:00:38,592 - display - [32mINFO[0m - display_item_header:129 - Incidente #4: INC000000016368
2025-06-26 11:00:38,593 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:00:38,593 - display - [32mINFO[0m - display_item_header:129 - Incidente #5: INC000000032662
2025-06-26 11:00:38,593 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:00:38,595 - display - [33mWARNING[0m - display_warning:174 - Mostrando 5 de 6 incidentes. Use '--todos' para ver todos.
2025-06-26 11:00:38,595 - open.mostrar_reporte - [32mINFO[0m - None:0 - Successfully completed mostrar reporte | Data: {'operation': 'mostrar reporte', 'archivo': 'C:\\Users\\<USER>\\Documents\\voice - copia\\bmc-remedy-container\\reporte_unificado.json'} | Data: {'operation': 'mostrar reporte', 'archivo': 'C:\\Users\\<USER>\\Documents\\voice - copia\\bmc-remedy-container\\reporte_unificado.json'}
2025-06-26 11:00:38,595 - procesar_todo - [32mINFO[0m - procesar_y_enviar:85 - Visualización en consola completada
2025-06-26 11:00:38,595 - procesar_todo - [32mINFO[0m - procesar_y_enviar:99 - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-06-26 11:00:41,129 - root - [32mINFO[0m - send:60 - Correo enviado <NAME_EMAIL>
2025-06-26 11:00:41,130 - procesar_todo - [32mINFO[0m - procesar_y_enviar:102 - Reporte enviado <NAME_EMAIL>
2025-06-26 11:00:41,130 - procesar_todo - [32mINFO[0m - main:178 - Proceso completado exitosamente para la cédula 1152213619
2025-06-26 11:00:41,168 - main.flujo_completo - [32mINFO[0m - None:0 - Successfully completed envío de reporte | Data: {'operation': 'envío de reporte', 'destinatario': '<EMAIL>'} | Data: {'operation': 'envío de reporte', 'destinatario': '<EMAIL>'}
2025-06-26 11:00:41,169 - main.flujo_completo - [32mINFO[0m - None:0 - Successfully completed flujo completo | Data: {'operation': 'flujo completo', 'cedula': '1152213619', 'tickets_encontrados': 0, 'incidentes_encontrados': 6} | Data: {'operation': 'flujo completo', 'cedula': '1152213619', 'tickets_encontrados': 0, 'incidentes_encontrados': 6}
2025-06-26 11:00:41,169 - main - [32mINFO[0m - None:0 - Successfully completed aplicación principal | Data: {'operation': 'aplicación principal', 'cedula': '1152213619', 'destinatario': '<EMAIL>'} | Data: {'operation': 'aplicación principal', 'cedula': '1152213619', 'destinatario': '<EMAIL>'}
2025-06-26 11:03:05,241 - main - [32mINFO[0m - None:0 - Starting aplicación principal | Data: {'operation': 'aplicación principal', 'cedula': '1152213619', 'destinatario': '<EMAIL>', 'todos': True, 'version': '1.0.0', 'debug_mode': False, 'silent_mode': False} | Data: {'operation': 'aplicación principal', 'cedula': '1152213619', 'destinatario': '<EMAIL>', 'todos': True, 'version': '1.0.0', 'debug_mode': False, 'silent_mode': False}
2025-06-26 11:03:05,241 - main.flujo_completo - [32mINFO[0m - None:0 - Starting flujo completo | Data: {'operation': 'flujo completo', 'cedula': '1152213619', 'destinatario': '<EMAIL>', 'todos': True} | Data: {'operation': 'flujo completo', 'cedula': '1152213619', 'destinatario': '<EMAIL>', 'todos': True}
2025-06-26 11:03:05,242 - main.flujo_completo - [32mINFO[0m - None:0 - Starting búsqueda de casos | Data: {'operation': 'búsqueda de casos', 'cedula': '1152213619', 'mostrar_todos': True} | Data: {'operation': 'búsqueda de casos', 'cedula': '1152213619', 'mostrar_todos': True}
2025-06-26 11:03:05,243 - main.flujo_completo - [32mINFO[0m - _ejecutar_busqueda_casos:86 - Ejecutando comando: C:\Users\<USER>\AppData\Local\anaconda3\python.exe open.py 1152213619 --todos
2025-06-26 11:03:08,395 - open.buscar_tickets - [32mINFO[0m - None:0 - Starting búsqueda de tickets | Data: {'operation': 'búsqueda de tickets', 'cedula': '1152213619', 'mostrar_todos': True} | Data: {'operation': 'búsqueda de tickets', 'cedula': '1152213619', 'mostrar_todos': True}
2025-06-26 11:03:09,013 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:78 - Buscando tickets (Work Orders) para cédula 1152213619
2025-06-26 11:03:09,473 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:181 - Se encontraron 0 work orders
2025-06-26 11:03:09,474 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:184 - Buscando incidentes para cédula 1152213619
2025-06-26 11:03:12,963 - open.mostrar_reporte - [32mINFO[0m - None:0 - Starting mostrar reporte | Data: {'operation': 'mostrar reporte', 'archivo': 'C:\\Users\\<USER>\\Documents\\voice - copia\\bmc-remedy-container\\reporte_unificado.json', 'mostrar_todos': True} | Data: {'operation': 'mostrar reporte', 'archivo': 'C:\\Users\\<USER>\\Documents\\voice - copia\\bmc-remedy-container\\reporte_unificado.json', 'mostrar_todos': True}
2025-06-26 11:03:12,964 - display - [32mINFO[0m - display_section_header:94 - INFORMACIÓN DE TICKETS
2025-06-26 11:03:12,965 - display - [32mINFO[0m - display_info_line:109 - Cédula: 1152213619
2025-06-26 11:03:12,965 - display - [32mINFO[0m - display_info_line:109 - Fecha de consulta: 2025-06-26 11:03:12
2025-06-26 11:03:12,965 - display - [32mINFO[0m - display_info_line:109 - Total de tickets: 0
2025-06-26 11:03:12,965 - display - [32mINFO[0m - display_info_line:109 - Tickets procesados: 0
2025-06-26 11:03:12,965 - display - [33mWARNING[0m - display_warning:174 - No se encontraron tickets.
2025-06-26 11:03:12,965 - display - [32mINFO[0m - display_section_header:94 - INFORMACIÓN DE INCIDENTES
2025-06-26 11:03:12,965 - display - [32mINFO[0m - display_info_line:109 - Login ID: 1152213619
2025-06-26 11:03:12,965 - display - [32mINFO[0m - display_info_line:109 - Fecha de consulta: 2025-06-26 11:03:12
2025-06-26 11:03:12,965 - display - [32mINFO[0m - display_info_line:109 - Total de incidentes: 6
2025-06-26 11:03:12,965 - display - [32mINFO[0m - display_info_line:109 - Incidentes procesados: 6
2025-06-26 11:03:12,966 - display - [32mINFO[0m - display_section_header:94 - INCIDENTES (6)
2025-06-26 11:03:12,966 - display - [32mINFO[0m - display_item_header:129 - Incidente #1: INC000000002266
2025-06-26 11:03:12,973 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:03:12,973 - display - [32mINFO[0m - display_item_header:129 - Incidente #2: INC000000015309
2025-06-26 11:03:12,974 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:03:12,974 - display - [32mINFO[0m - display_item_header:129 - Incidente #3: INC000000015311
2025-06-26 11:03:12,975 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:03:12,975 - display - [32mINFO[0m - display_item_header:129 - Incidente #4: INC000000016368
2025-06-26 11:03:12,976 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:03:12,976 - display - [32mINFO[0m - display_item_header:129 - Incidente #5: INC000000032662
2025-06-26 11:03:12,977 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:03:12,977 - display - [32mINFO[0m - display_item_header:129 - Incidente #6: INC000000056308
2025-06-26 11:03:12,977 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:03:12,978 - open.mostrar_reporte - [32mINFO[0m - None:0 - Successfully completed mostrar reporte | Data: {'operation': 'mostrar reporte', 'archivo': 'C:\\Users\\<USER>\\Documents\\voice - copia\\bmc-remedy-container\\reporte_unificado.json'} | Data: {'operation': 'mostrar reporte', 'archivo': 'C:\\Users\\<USER>\\Documents\\voice - copia\\bmc-remedy-container\\reporte_unificado.json'}
2025-06-26 11:03:13,015 - main.flujo_completo - [32mINFO[0m - None:0 - Successfully completed búsqueda de casos | Data: {'operation': 'búsqueda de casos', 'tickets_encontrados': 0, 'incidentes_encontrados': 6} | Data: {'operation': 'búsqueda de casos', 'tickets_encontrados': 0, 'incidentes_encontrados': 6}
2025-06-26 11:03:13,015 - main.flujo_completo - [32mINFO[0m - None:0 - Starting envío de reporte | Data: {'operation': 'envío de reporte', 'cedula': '1152213619', 'destinatario': '<EMAIL>'} | Data: {'operation': 'envío de reporte', 'cedula': '1152213619', 'destinatario': '<EMAIL>'}
2025-06-26 11:03:13,016 - main.flujo_completo - [32mINFO[0m - _ejecutar_envio_reporte:141 - Ejecutando comando: C:\Users\<USER>\AppData\Local\anaconda3\python.exe procesar_todo.py 1152213619 --destinatario <EMAIL>
2025-06-26 11:03:16,657 - open.mostrar_reporte - [32mINFO[0m - None:0 - Starting mostrar reporte | Data: {'operation': 'mostrar reporte', 'archivo': 'C:\\Users\\<USER>\\Documents\\voice - copia\\bmc-remedy-container\\reporte_unificado.json', 'mostrar_todos': False} | Data: {'operation': 'mostrar reporte', 'archivo': 'C:\\Users\\<USER>\\Documents\\voice - copia\\bmc-remedy-container\\reporte_unificado.json', 'mostrar_todos': False}
2025-06-26 11:03:16,660 - display - [32mINFO[0m - display_section_header:94 - INFORMACIÓN DE TICKETS
2025-06-26 11:03:16,660 - display - [32mINFO[0m - display_info_line:109 - Cédula: 1152213619
2025-06-26 11:03:16,660 - display - [32mINFO[0m - display_info_line:109 - Fecha de consulta: 2025-06-26 11:03:12
2025-06-26 11:03:16,661 - display - [32mINFO[0m - display_info_line:109 - Total de tickets: 0
2025-06-26 11:03:16,661 - display - [32mINFO[0m - display_info_line:109 - Tickets procesados: 0
2025-06-26 11:03:16,661 - display - [33mWARNING[0m - display_warning:174 - No se encontraron tickets.
2025-06-26 11:03:16,661 - display - [32mINFO[0m - display_section_header:94 - INFORMACIÓN DE INCIDENTES
2025-06-26 11:03:16,661 - display - [32mINFO[0m - display_info_line:109 - Login ID: 1152213619
2025-06-26 11:03:16,661 - display - [32mINFO[0m - display_info_line:109 - Fecha de consulta: 2025-06-26 11:03:12
2025-06-26 11:03:16,661 - display - [32mINFO[0m - display_info_line:109 - Total de incidentes: 6
2025-06-26 11:03:16,661 - display - [32mINFO[0m - display_info_line:109 - Incidentes procesados: 6
2025-06-26 11:03:16,661 - display - [32mINFO[0m - display_section_header:94 - INCIDENTES (6)
2025-06-26 11:03:16,661 - display - [32mINFO[0m - display_item_header:129 - Incidente #1: INC000000002266
2025-06-26 11:03:16,672 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:03:16,672 - display - [32mINFO[0m - display_item_header:129 - Incidente #2: INC000000015309
2025-06-26 11:03:16,673 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:03:16,673 - display - [32mINFO[0m - display_item_header:129 - Incidente #3: INC000000015311
2025-06-26 11:03:16,675 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:03:16,675 - display - [32mINFO[0m - display_item_header:129 - Incidente #4: INC000000016368
2025-06-26 11:03:16,675 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:03:16,675 - display - [32mINFO[0m - display_item_header:129 - Incidente #5: INC000000032662
2025-06-26 11:03:16,677 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:03:16,677 - display - [33mWARNING[0m - display_warning:174 - Mostrando 5 de 6 incidentes. Use '--todos' para ver todos.
2025-06-26 11:03:16,677 - open.mostrar_reporte - [32mINFO[0m - None:0 - Successfully completed mostrar reporte | Data: {'operation': 'mostrar reporte', 'archivo': 'C:\\Users\\<USER>\\Documents\\voice - copia\\bmc-remedy-container\\reporte_unificado.json'} | Data: {'operation': 'mostrar reporte', 'archivo': 'C:\\Users\\<USER>\\Documents\\voice - copia\\bmc-remedy-container\\reporte_unificado.json'}
2025-06-26 11:03:16,677 - procesar_todo - [32mINFO[0m - procesar_y_enviar:85 - Visualización en consola completada
2025-06-26 11:03:16,678 - procesar_todo - [32mINFO[0m - procesar_y_enviar:99 - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-06-26 11:03:19,434 - root - [32mINFO[0m - send:60 - Correo enviado <NAME_EMAIL>
2025-06-26 11:03:19,435 - procesar_todo - [32mINFO[0m - procesar_y_enviar:102 - Reporte enviado <NAME_EMAIL>
2025-06-26 11:03:19,435 - procesar_todo - [32mINFO[0m - main:178 - Proceso completado exitosamente para la cédula 1152213619
2025-06-26 11:03:19,476 - main.flujo_completo - [32mINFO[0m - None:0 - Successfully completed envío de reporte | Data: {'operation': 'envío de reporte', 'destinatario': '<EMAIL>'} | Data: {'operation': 'envío de reporte', 'destinatario': '<EMAIL>'}
2025-06-26 11:03:19,476 - main.flujo_completo - [32mINFO[0m - None:0 - Successfully completed flujo completo | Data: {'operation': 'flujo completo', 'cedula': '1152213619', 'tickets_encontrados': 0, 'incidentes_encontrados': 6} | Data: {'operation': 'flujo completo', 'cedula': '1152213619', 'tickets_encontrados': 0, 'incidentes_encontrados': 6}
2025-06-26 11:03:19,476 - main - [32mINFO[0m - None:0 - Successfully completed aplicación principal | Data: {'operation': 'aplicación principal', 'cedula': '1152213619', 'destinatario': '<EMAIL>'} | Data: {'operation': 'aplicación principal', 'cedula': '1152213619', 'destinatario': '<EMAIL>'}
2025-06-26 11:04:20,177 - main - [32mINFO[0m - None:0 - Starting aplicación principal | Data: {'operation': 'aplicación principal', 'cedula': '1152213619', 'destinatario': '<EMAIL>', 'todos': True, 'version': '1.0.0', 'debug_mode': False, 'silent_mode': False} | Data: {'operation': 'aplicación principal', 'cedula': '1152213619', 'destinatario': '<EMAIL>', 'todos': True, 'version': '1.0.0', 'debug_mode': False, 'silent_mode': False}
2025-06-26 11:04:20,177 - main.flujo_completo - [32mINFO[0m - None:0 - Starting flujo completo | Data: {'operation': 'flujo completo', 'cedula': '1152213619', 'destinatario': '<EMAIL>', 'todos': True} | Data: {'operation': 'flujo completo', 'cedula': '1152213619', 'destinatario': '<EMAIL>', 'todos': True}
2025-06-26 11:04:20,178 - main.flujo_completo - [32mINFO[0m - None:0 - Starting búsqueda de casos | Data: {'operation': 'búsqueda de casos', 'cedula': '1152213619', 'mostrar_todos': True} | Data: {'operation': 'búsqueda de casos', 'cedula': '1152213619', 'mostrar_todos': True}
2025-06-26 11:04:20,178 - main.flujo_completo - [32mINFO[0m - _ejecutar_busqueda_casos:86 - Ejecutando comando: C:\Users\<USER>\AppData\Local\anaconda3\python.exe open.py 1152213619 --todos
2025-06-26 11:04:23,308 - open.buscar_tickets - [32mINFO[0m - None:0 - Starting búsqueda de tickets | Data: {'operation': 'búsqueda de tickets', 'cedula': '1152213619', 'mostrar_todos': True} | Data: {'operation': 'búsqueda de tickets', 'cedula': '1152213619', 'mostrar_todos': True}
2025-06-26 11:04:23,789 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:78 - Buscando tickets (Work Orders) para cédula 1152213619
2025-06-26 11:04:24,301 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:181 - Se encontraron 0 work orders
2025-06-26 11:04:24,301 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:184 - Buscando incidentes para cédula 1152213619
2025-06-26 11:04:27,665 - open.mostrar_reporte - [32mINFO[0m - None:0 - Starting mostrar reporte | Data: {'operation': 'mostrar reporte', 'archivo': 'C:\\Users\\<USER>\\Documents\\voice - copia\\bmc-remedy-container\\reporte_unificado.json', 'mostrar_todos': True} | Data: {'operation': 'mostrar reporte', 'archivo': 'C:\\Users\\<USER>\\Documents\\voice - copia\\bmc-remedy-container\\reporte_unificado.json', 'mostrar_todos': True}
2025-06-26 11:04:27,667 - display - [32mINFO[0m - display_section_header:94 - INFORMACIÓN DE TICKETS
2025-06-26 11:04:27,667 - display - [32mINFO[0m - display_info_line:109 - Cédula: 1152213619
2025-06-26 11:04:27,667 - display - [32mINFO[0m - display_info_line:109 - Fecha de consulta: 2025-06-26 11:04:27
2025-06-26 11:04:27,668 - display - [32mINFO[0m - display_info_line:109 - Total de tickets: 0
2025-06-26 11:04:27,668 - display - [32mINFO[0m - display_info_line:109 - Tickets procesados: 0
2025-06-26 11:04:27,668 - display - [33mWARNING[0m - display_warning:174 - No se encontraron tickets.
2025-06-26 11:04:27,668 - display - [32mINFO[0m - display_section_header:94 - INFORMACIÓN DE INCIDENTES
2025-06-26 11:04:27,668 - display - [32mINFO[0m - display_info_line:109 - Login ID: 1152213619
2025-06-26 11:04:27,668 - display - [32mINFO[0m - display_info_line:109 - Fecha de consulta: 2025-06-26 11:04:27
2025-06-26 11:04:27,668 - display - [32mINFO[0m - display_info_line:109 - Total de incidentes: 6
2025-06-26 11:04:27,668 - display - [32mINFO[0m - display_info_line:109 - Incidentes procesados: 6
2025-06-26 11:04:27,668 - display - [32mINFO[0m - display_section_header:94 - INCIDENTES (6)
2025-06-26 11:04:27,669 - display - [32mINFO[0m - display_item_header:129 - Incidente #1: INC000000002266
2025-06-26 11:04:27,676 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:04:27,676 - display - [32mINFO[0m - display_item_header:129 - Incidente #2: INC000000015309
2025-06-26 11:04:27,677 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:04:27,677 - display - [32mINFO[0m - display_item_header:129 - Incidente #3: INC000000015311
2025-06-26 11:04:27,678 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:04:27,678 - display - [32mINFO[0m - display_item_header:129 - Incidente #4: INC000000016368
2025-06-26 11:04:27,679 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:04:27,679 - display - [32mINFO[0m - display_item_header:129 - Incidente #5: INC000000032662
2025-06-26 11:04:27,680 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:04:27,680 - display - [32mINFO[0m - display_item_header:129 - Incidente #6: INC000000056308
2025-06-26 11:04:27,680 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:04:27,680 - open.mostrar_reporte - [32mINFO[0m - None:0 - Successfully completed mostrar reporte | Data: {'operation': 'mostrar reporte', 'archivo': 'C:\\Users\\<USER>\\Documents\\voice - copia\\bmc-remedy-container\\reporte_unificado.json'} | Data: {'operation': 'mostrar reporte', 'archivo': 'C:\\Users\\<USER>\\Documents\\voice - copia\\bmc-remedy-container\\reporte_unificado.json'}
2025-06-26 11:04:27,721 - main.flujo_completo - [32mINFO[0m - None:0 - Successfully completed búsqueda de casos | Data: {'operation': 'búsqueda de casos', 'tickets_encontrados': 0, 'incidentes_encontrados': 6} | Data: {'operation': 'búsqueda de casos', 'tickets_encontrados': 0, 'incidentes_encontrados': 6}
2025-06-26 11:04:27,721 - main.flujo_completo - [32mINFO[0m - None:0 - Starting envío de reporte | Data: {'operation': 'envío de reporte', 'cedula': '1152213619', 'destinatario': '<EMAIL>'} | Data: {'operation': 'envío de reporte', 'cedula': '1152213619', 'destinatario': '<EMAIL>'}
2025-06-26 11:04:27,721 - main.flujo_completo - [32mINFO[0m - _ejecutar_envio_reporte:141 - Ejecutando comando: C:\Users\<USER>\AppData\Local\anaconda3\python.exe procesar_todo.py 1152213619 --destinatario <EMAIL>
2025-06-26 11:04:30,572 - open.mostrar_reporte - [32mINFO[0m - None:0 - Starting mostrar reporte | Data: {'operation': 'mostrar reporte', 'archivo': 'C:\\Users\\<USER>\\Documents\\voice - copia\\bmc-remedy-container\\reporte_unificado.json', 'mostrar_todos': False} | Data: {'operation': 'mostrar reporte', 'archivo': 'C:\\Users\\<USER>\\Documents\\voice - copia\\bmc-remedy-container\\reporte_unificado.json', 'mostrar_todos': False}
2025-06-26 11:04:30,574 - display - [32mINFO[0m - display_section_header:94 - INFORMACIÓN DE TICKETS
2025-06-26 11:04:30,574 - display - [32mINFO[0m - display_info_line:109 - Cédula: 1152213619
2025-06-26 11:04:30,574 - display - [32mINFO[0m - display_info_line:109 - Fecha de consulta: 2025-06-26 11:04:27
2025-06-26 11:04:30,574 - display - [32mINFO[0m - display_info_line:109 - Total de tickets: 0
2025-06-26 11:04:30,574 - display - [32mINFO[0m - display_info_line:109 - Tickets procesados: 0
2025-06-26 11:04:30,574 - display - [33mWARNING[0m - display_warning:174 - No se encontraron tickets.
2025-06-26 11:04:30,574 - display - [32mINFO[0m - display_section_header:94 - INFORMACIÓN DE INCIDENTES
2025-06-26 11:04:30,574 - display - [32mINFO[0m - display_info_line:109 - Login ID: 1152213619
2025-06-26 11:04:30,574 - display - [32mINFO[0m - display_info_line:109 - Fecha de consulta: 2025-06-26 11:04:27
2025-06-26 11:04:30,575 - display - [32mINFO[0m - display_info_line:109 - Total de incidentes: 6
2025-06-26 11:04:30,575 - display - [32mINFO[0m - display_info_line:109 - Incidentes procesados: 6
2025-06-26 11:04:30,575 - display - [32mINFO[0m - display_section_header:94 - INCIDENTES (6)
2025-06-26 11:04:30,575 - display - [32mINFO[0m - display_item_header:129 - Incidente #1: INC000000002266
2025-06-26 11:04:30,584 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:04:30,584 - display - [32mINFO[0m - display_item_header:129 - Incidente #2: INC000000015309
2025-06-26 11:04:30,585 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:04:30,585 - display - [32mINFO[0m - display_item_header:129 - Incidente #3: INC000000015311
2025-06-26 11:04:30,585 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:04:30,586 - display - [32mINFO[0m - display_item_header:129 - Incidente #4: INC000000016368
2025-06-26 11:04:30,586 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:04:30,586 - display - [32mINFO[0m - display_item_header:129 - Incidente #5: INC000000032662
2025-06-26 11:04:30,587 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:04:30,587 - display - [33mWARNING[0m - display_warning:174 - Mostrando 5 de 6 incidentes. Use '--todos' para ver todos.
2025-06-26 11:04:30,587 - open.mostrar_reporte - [32mINFO[0m - None:0 - Successfully completed mostrar reporte | Data: {'operation': 'mostrar reporte', 'archivo': 'C:\\Users\\<USER>\\Documents\\voice - copia\\bmc-remedy-container\\reporte_unificado.json'} | Data: {'operation': 'mostrar reporte', 'archivo': 'C:\\Users\\<USER>\\Documents\\voice - copia\\bmc-remedy-container\\reporte_unificado.json'}
2025-06-26 11:04:30,588 - procesar_todo - [32mINFO[0m - procesar_y_enviar:85 - Visualización en consola completada
2025-06-26 11:04:30,588 - procesar_todo - [32mINFO[0m - procesar_y_enviar:99 - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-06-26 11:04:33,887 - root - [32mINFO[0m - send:60 - Correo enviado <NAME_EMAIL>
2025-06-26 11:04:33,887 - procesar_todo - [32mINFO[0m - procesar_y_enviar:102 - Reporte enviado <NAME_EMAIL>
2025-06-26 11:04:33,889 - procesar_todo - [32mINFO[0m - main:178 - Proceso completado exitosamente para la cédula 1152213619
2025-06-26 11:04:33,929 - main.flujo_completo - [32mINFO[0m - None:0 - Successfully completed envío de reporte | Data: {'operation': 'envío de reporte', 'destinatario': '<EMAIL>'} | Data: {'operation': 'envío de reporte', 'destinatario': '<EMAIL>'}
2025-06-26 11:04:33,930 - main.flujo_completo - [32mINFO[0m - None:0 - Successfully completed flujo completo | Data: {'operation': 'flujo completo', 'cedula': '1152213619', 'tickets_encontrados': 0, 'incidentes_encontrados': 6} | Data: {'operation': 'flujo completo', 'cedula': '1152213619', 'tickets_encontrados': 0, 'incidentes_encontrados': 6}
2025-06-26 11:04:33,930 - main - [32mINFO[0m - None:0 - Successfully completed aplicación principal | Data: {'operation': 'aplicación principal', 'cedula': '1152213619', 'destinatario': '<EMAIL>'} | Data: {'operation': 'aplicación principal', 'cedula': '1152213619', 'destinatario': '<EMAIL>'}
2025-06-26 11:11:32,004 - main - [32mINFO[0m - None:0 - Starting aplicación principal | Data: {'operation': 'aplicación principal', 'cedula': '1143936436', 'destinatario': '<EMAIL>', 'todos': True, 'version': '1.0.0', 'debug_mode': False, 'silent_mode': False} | Data: {'operation': 'aplicación principal', 'cedula': '1143936436', 'destinatario': '<EMAIL>', 'todos': True, 'version': '1.0.0', 'debug_mode': False, 'silent_mode': False}
2025-06-26 11:11:32,006 - main.flujo_completo - [32mINFO[0m - None:0 - Starting flujo completo | Data: {'operation': 'flujo completo', 'cedula': '1143936436', 'destinatario': '<EMAIL>', 'todos': True} | Data: {'operation': 'flujo completo', 'cedula': '1143936436', 'destinatario': '<EMAIL>', 'todos': True}
2025-06-26 11:11:32,007 - main.flujo_completo - [32mINFO[0m - None:0 - Starting búsqueda de casos | Data: {'operation': 'búsqueda de casos', 'cedula': '1143936436', 'mostrar_todos': True} | Data: {'operation': 'búsqueda de casos', 'cedula': '1143936436', 'mostrar_todos': True}
2025-06-26 11:11:32,008 - main.flujo_completo - [32mINFO[0m - _ejecutar_busqueda_casos:86 - Ejecutando comando: C:\Users\<USER>\AppData\Local\anaconda3\python.exe open.py 1143936436 --todos
2025-06-26 11:11:41,502 - open.buscar_tickets - [32mINFO[0m - None:0 - Starting búsqueda de tickets | Data: {'operation': 'búsqueda de tickets', 'cedula': '1143936436', 'mostrar_todos': True} | Data: {'operation': 'búsqueda de tickets', 'cedula': '1143936436', 'mostrar_todos': True}
2025-06-26 11:11:42,017 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:78 - Buscando tickets (Work Orders) para cédula 1143936436
2025-06-26 11:11:42,487 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:181 - Se encontraron 0 work orders
2025-06-26 11:11:42,487 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:184 - Buscando incidentes para cédula 1143936436
2025-06-26 11:11:43,158 - main.flujo_completo - [32mINFO[0m - None:0 - Successfully completed búsqueda de casos | Data: {'operation': 'búsqueda de casos', 'tickets_encontrados': 0, 'incidentes_encontrados': 6} | Data: {'operation': 'búsqueda de casos', 'tickets_encontrados': 0, 'incidentes_encontrados': 6}
2025-06-26 11:11:43,159 - main.flujo_completo - [32mINFO[0m - None:0 - Starting envío de reporte | Data: {'operation': 'envío de reporte', 'cedula': '1143936436', 'destinatario': '<EMAIL>'} | Data: {'operation': 'envío de reporte', 'cedula': '1143936436', 'destinatario': '<EMAIL>'}
2025-06-26 11:11:43,159 - main.flujo_completo - [32mINFO[0m - _ejecutar_envio_reporte:141 - Ejecutando comando: C:\Users\<USER>\AppData\Local\anaconda3\python.exe procesar_todo.py 1143936436 --destinatario <EMAIL>
2025-06-26 11:11:46,970 - open.mostrar_reporte - [32mINFO[0m - None:0 - Starting mostrar reporte | Data: {'operation': 'mostrar reporte', 'archivo': 'C:\\Users\\<USER>\\Documents\\voice - copia\\bmc-remedy-container\\reporte_unificado.json', 'mostrar_todos': False} | Data: {'operation': 'mostrar reporte', 'archivo': 'C:\\Users\\<USER>\\Documents\\voice - copia\\bmc-remedy-container\\reporte_unificado.json', 'mostrar_todos': False}
2025-06-26 11:11:46,972 - display - [32mINFO[0m - display_section_header:94 - INFORMACIÓN DE TICKETS
2025-06-26 11:11:46,972 - display - [32mINFO[0m - display_info_line:109 - Cédula: 1152213619
2025-06-26 11:11:46,972 - display - [32mINFO[0m - display_info_line:109 - Fecha de consulta: 2025-06-26 11:04:27
2025-06-26 11:11:46,973 - display - [32mINFO[0m - display_info_line:109 - Total de tickets: 0
2025-06-26 11:11:46,973 - display - [32mINFO[0m - display_info_line:109 - Tickets procesados: 0
2025-06-26 11:11:46,973 - display - [33mWARNING[0m - display_warning:174 - No se encontraron tickets.
2025-06-26 11:11:46,973 - display - [32mINFO[0m - display_section_header:94 - INFORMACIÓN DE INCIDENTES
2025-06-26 11:11:46,973 - display - [32mINFO[0m - display_info_line:109 - Login ID: 1152213619
2025-06-26 11:11:46,974 - display - [32mINFO[0m - display_info_line:109 - Fecha de consulta: 2025-06-26 11:04:27
2025-06-26 11:11:46,974 - display - [32mINFO[0m - display_info_line:109 - Total de incidentes: 6
2025-06-26 11:11:46,974 - display - [32mINFO[0m - display_info_line:109 - Incidentes procesados: 6
2025-06-26 11:11:46,974 - display - [32mINFO[0m - display_section_header:94 - INCIDENTES (6)
2025-06-26 11:11:46,974 - display - [32mINFO[0m - display_item_header:129 - Incidente #1: INC000000002266
2025-06-26 11:11:46,989 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:11:46,989 - display - [32mINFO[0m - display_item_header:129 - Incidente #2: INC000000015309
2025-06-26 11:11:46,990 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:11:46,990 - display - [32mINFO[0m - display_item_header:129 - Incidente #3: INC000000015311
2025-06-26 11:11:46,991 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:11:46,992 - display - [32mINFO[0m - display_item_header:129 - Incidente #4: INC000000016368
2025-06-26 11:11:46,993 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:11:46,994 - display - [32mINFO[0m - display_item_header:129 - Incidente #5: INC000000032662
2025-06-26 11:11:46,995 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:11:46,995 - display - [33mWARNING[0m - display_warning:174 - Mostrando 5 de 6 incidentes. Use '--todos' para ver todos.
2025-06-26 11:11:46,995 - open.mostrar_reporte - [32mINFO[0m - None:0 - Successfully completed mostrar reporte | Data: {'operation': 'mostrar reporte', 'archivo': 'C:\\Users\\<USER>\\Documents\\voice - copia\\bmc-remedy-container\\reporte_unificado.json'} | Data: {'operation': 'mostrar reporte', 'archivo': 'C:\\Users\\<USER>\\Documents\\voice - copia\\bmc-remedy-container\\reporte_unificado.json'}
2025-06-26 11:11:46,996 - procesar_todo - [32mINFO[0m - procesar_y_enviar:85 - Visualización en consola completada
2025-06-26 11:11:46,996 - procesar_todo - [32mINFO[0m - procesar_y_enviar:99 - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-06-26 11:11:49,746 - root - [32mINFO[0m - send:60 - Correo enviado <NAME_EMAIL>
2025-06-26 11:11:49,748 - procesar_todo - [32mINFO[0m - procesar_y_enviar:102 - Reporte enviado <NAME_EMAIL>
2025-06-26 11:11:49,748 - procesar_todo - [32mINFO[0m - main:178 - Proceso completado exitosamente para la cédula 1143936436
2025-06-26 11:11:49,789 - main.flujo_completo - [32mINFO[0m - None:0 - Successfully completed envío de reporte | Data: {'operation': 'envío de reporte', 'destinatario': '<EMAIL>'} | Data: {'operation': 'envío de reporte', 'destinatario': '<EMAIL>'}
2025-06-26 11:11:49,789 - main.flujo_completo - [32mINFO[0m - None:0 - Successfully completed flujo completo | Data: {'operation': 'flujo completo', 'cedula': '1143936436', 'tickets_encontrados': 0, 'incidentes_encontrados': 6} | Data: {'operation': 'flujo completo', 'cedula': '1143936436', 'tickets_encontrados': 0, 'incidentes_encontrados': 6}
2025-06-26 11:11:49,790 - main - [32mINFO[0m - None:0 - Successfully completed aplicación principal | Data: {'operation': 'aplicación principal', 'cedula': '1143936436', 'destinatario': '<EMAIL>'} | Data: {'operation': 'aplicación principal', 'cedula': '1143936436', 'destinatario': '<EMAIL>'}
2025-06-26 11:13:11,149 - main - [32mINFO[0m - None:0 - Starting aplicación principal | Data: {'operation': 'aplicación principal', 'cedula': '1143936436', 'destinatario': '<EMAIL>', 'todos': True, 'version': '1.0.0', 'debug_mode': False, 'silent_mode': False} | Data: {'operation': 'aplicación principal', 'cedula': '1143936436', 'destinatario': '<EMAIL>', 'todos': True, 'version': '1.0.0', 'debug_mode': False, 'silent_mode': False}
2025-06-26 11:13:11,149 - main.flujo_completo - [32mINFO[0m - None:0 - Starting flujo completo | Data: {'operation': 'flujo completo', 'cedula': '1143936436', 'destinatario': '<EMAIL>', 'todos': True} | Data: {'operation': 'flujo completo', 'cedula': '1143936436', 'destinatario': '<EMAIL>', 'todos': True}
2025-06-26 11:13:11,149 - main.flujo_completo - [32mINFO[0m - None:0 - Starting búsqueda de casos | Data: {'operation': 'búsqueda de casos', 'cedula': '1143936436', 'mostrar_todos': True} | Data: {'operation': 'búsqueda de casos', 'cedula': '1143936436', 'mostrar_todos': True}
2025-06-26 11:13:11,150 - main.flujo_completo - [32mINFO[0m - _ejecutar_busqueda_casos:86 - Ejecutando comando: C:\Users\<USER>\AppData\Local\anaconda3\python.exe open.py 1143936436 --todos
2025-06-26 11:13:14,426 - open.buscar_tickets - [32mINFO[0m - None:0 - Starting búsqueda de tickets | Data: {'operation': 'búsqueda de tickets', 'cedula': '1143936436', 'mostrar_todos': True} | Data: {'operation': 'búsqueda de tickets', 'cedula': '1143936436', 'mostrar_todos': True}
2025-06-26 11:13:15,016 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:78 - Buscando tickets (Work Orders) para cédula 1143936436
2025-06-26 11:13:15,480 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:181 - Se encontraron 0 work orders
2025-06-26 11:13:15,481 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:184 - Buscando incidentes para cédula 1143936436
2025-06-26 11:13:16,042 - main.flujo_completo - [32mINFO[0m - None:0 - Successfully completed búsqueda de casos | Data: {'operation': 'búsqueda de casos', 'tickets_encontrados': 0, 'incidentes_encontrados': 6} | Data: {'operation': 'búsqueda de casos', 'tickets_encontrados': 0, 'incidentes_encontrados': 6}
2025-06-26 11:13:16,042 - main.flujo_completo - [32mINFO[0m - None:0 - Starting envío de reporte | Data: {'operation': 'envío de reporte', 'cedula': '1143936436', 'destinatario': '<EMAIL>'} | Data: {'operation': 'envío de reporte', 'cedula': '1143936436', 'destinatario': '<EMAIL>'}
2025-06-26 11:13:16,044 - main.flujo_completo - [32mINFO[0m - _ejecutar_envio_reporte:141 - Ejecutando comando: C:\Users\<USER>\AppData\Local\anaconda3\python.exe procesar_todo.py 1143936436 --destinatario <EMAIL>
2025-06-26 11:13:18,937 - open.mostrar_reporte - [32mINFO[0m - None:0 - Starting mostrar reporte | Data: {'operation': 'mostrar reporte', 'archivo': 'C:\\Users\\<USER>\\Documents\\voice - copia\\bmc-remedy-container\\reporte_unificado.json', 'mostrar_todos': False} | Data: {'operation': 'mostrar reporte', 'archivo': 'C:\\Users\\<USER>\\Documents\\voice - copia\\bmc-remedy-container\\reporte_unificado.json', 'mostrar_todos': False}
2025-06-26 11:13:18,939 - display - [32mINFO[0m - display_section_header:94 - INFORMACIÓN DE TICKETS
2025-06-26 11:13:18,939 - display - [32mINFO[0m - display_info_line:109 - Cédula: 1152213619
2025-06-26 11:13:18,939 - display - [32mINFO[0m - display_info_line:109 - Fecha de consulta: 2025-06-26 11:04:27
2025-06-26 11:13:18,939 - display - [32mINFO[0m - display_info_line:109 - Total de tickets: 0
2025-06-26 11:13:18,939 - display - [32mINFO[0m - display_info_line:109 - Tickets procesados: 0
2025-06-26 11:13:18,939 - display - [33mWARNING[0m - display_warning:174 - No se encontraron tickets.
2025-06-26 11:13:18,939 - display - [32mINFO[0m - display_section_header:94 - INFORMACIÓN DE INCIDENTES
2025-06-26 11:13:18,940 - display - [32mINFO[0m - display_info_line:109 - Login ID: 1152213619
2025-06-26 11:13:18,940 - display - [32mINFO[0m - display_info_line:109 - Fecha de consulta: 2025-06-26 11:04:27
2025-06-26 11:13:18,940 - display - [32mINFO[0m - display_info_line:109 - Total de incidentes: 6
2025-06-26 11:13:18,940 - display - [32mINFO[0m - display_info_line:109 - Incidentes procesados: 6
2025-06-26 11:13:18,940 - display - [32mINFO[0m - display_section_header:94 - INCIDENTES (6)
2025-06-26 11:13:18,940 - display - [32mINFO[0m - display_item_header:129 - Incidente #1: INC000000002266
2025-06-26 11:13:18,950 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:13:18,950 - display - [32mINFO[0m - display_item_header:129 - Incidente #2: INC000000015309
2025-06-26 11:13:18,951 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:13:18,951 - display - [32mINFO[0m - display_item_header:129 - Incidente #3: INC000000015311
2025-06-26 11:13:18,951 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:13:18,952 - display - [32mINFO[0m - display_item_header:129 - Incidente #4: INC000000016368
2025-06-26 11:13:18,953 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:13:18,953 - display - [32mINFO[0m - display_item_header:129 - Incidente #5: INC000000032662
2025-06-26 11:13:18,953 - display - [32mINFO[0m - display_note_header:156 - Displaying last note
2025-06-26 11:13:18,954 - display - [33mWARNING[0m - display_warning:174 - Mostrando 5 de 6 incidentes. Use '--todos' para ver todos.
2025-06-26 11:13:18,954 - open.mostrar_reporte - [32mINFO[0m - None:0 - Successfully completed mostrar reporte | Data: {'operation': 'mostrar reporte', 'archivo': 'C:\\Users\\<USER>\\Documents\\voice - copia\\bmc-remedy-container\\reporte_unificado.json'} | Data: {'operation': 'mostrar reporte', 'archivo': 'C:\\Users\\<USER>\\Documents\\voice - copia\\bmc-remedy-container\\reporte_unificado.json'}
2025-06-26 11:13:18,954 - procesar_todo - [32mINFO[0m - procesar_y_enviar:85 - Visualización en consola completada
2025-06-26 11:13:18,954 - procesar_todo - [32mINFO[0m - procesar_y_enviar:99 - Enviando <NAME_EMAIL> usando enviar_reporte.py...
2025-06-26 11:13:21,469 - root - [32mINFO[0m - send:60 - Correo enviado <NAME_EMAIL>
2025-06-26 11:13:21,471 - procesar_todo - [32mINFO[0m - procesar_y_enviar:102 - Reporte enviado <NAME_EMAIL>
2025-06-26 11:13:21,472 - procesar_todo - [32mINFO[0m - main:178 - Proceso completado exitosamente para la cédula 1143936436
2025-06-26 11:13:21,507 - main.flujo_completo - [32mINFO[0m - None:0 - Successfully completed envío de reporte | Data: {'operation': 'envío de reporte', 'destinatario': '<EMAIL>'} | Data: {'operation': 'envío de reporte', 'destinatario': '<EMAIL>'}
2025-06-26 11:13:21,507 - main.flujo_completo - [32mINFO[0m - None:0 - Successfully completed flujo completo | Data: {'operation': 'flujo completo', 'cedula': '1143936436', 'tickets_encontrados': 0, 'incidentes_encontrados': 6} | Data: {'operation': 'flujo completo', 'cedula': '1143936436', 'tickets_encontrados': 0, 'incidentes_encontrados': 6}
2025-06-26 11:13:21,508 - main - [32mINFO[0m - None:0 - Successfully completed aplicación principal | Data: {'operation': 'aplicación principal', 'cedula': '1143936436', 'destinatario': '<EMAIL>'} | Data: {'operation': 'aplicación principal', 'cedula': '1143936436', 'destinatario': '<EMAIL>'}
2025-06-26 11:21:08,009 - main - [32mINFO[0m - None:0 - Starting aplicación principal | Data: {'operation': 'aplicación principal', 'cedula': '1143936436', 'destinatario': '<EMAIL>', 'todos': True, 'version': '1.0.0', 'debug_mode': False, 'silent_mode': False} | Data: {'operation': 'aplicación principal', 'cedula': '1143936436', 'destinatario': '<EMAIL>', 'todos': True, 'version': '1.0.0', 'debug_mode': False, 'silent_mode': False}
2025-06-26 11:21:08,009 - main.flujo_completo - [32mINFO[0m - None:0 - Starting flujo completo | Data: {'operation': 'flujo completo', 'cedula': '1143936436', 'destinatario': '<EMAIL>', 'todos': True} | Data: {'operation': 'flujo completo', 'cedula': '1143936436', 'destinatario': '<EMAIL>', 'todos': True}
2025-06-26 11:21:08,010 - main.flujo_completo - [32mINFO[0m - None:0 - Starting búsqueda de casos | Data: {'operation': 'búsqueda de casos', 'cedula': '1143936436', 'mostrar_todos': True} | Data: {'operation': 'búsqueda de casos', 'cedula': '1143936436', 'mostrar_todos': True}
2025-06-26 11:21:08,010 - main.flujo_completo - [32mINFO[0m - _ejecutar_busqueda_casos:86 - Ejecutando comando: C:\Users\<USER>\AppData\Local\anaconda3\python.exe open.py 1143936436 --todos
2025-06-26 11:21:11,982 - open.buscar_tickets - [32mINFO[0m - None:0 - Starting búsqueda de tickets | Data: {'operation': 'búsqueda de tickets', 'cedula': '1143936436', 'mostrar_todos': True} | Data: {'operation': 'búsqueda de tickets', 'cedula': '1143936436', 'mostrar_todos': True}
2025-06-26 11:21:12,661 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:78 - Buscando tickets (Work Orders) para cédula 1143936436
2025-06-26 11:21:13,132 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:181 - Se encontraron 0 work orders
2025-06-26 11:21:13,133 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:184 - Buscando incidentes para cédula 1143936436
2025-06-26 11:21:13,702 - main.flujo_completo - [33mWARNING[0m - _contar_casos_encontrados:200 - Archivo de reporte no encontrado: C:\Users\<USER>\Documents\voice - copia\bmc-remedy-container\reporte_unificado.json
2025-06-26 11:21:13,703 - main.flujo_completo - [32mINFO[0m - None:0 - Successfully completed búsqueda de casos | Data: {'operation': 'búsqueda de casos', 'tickets_encontrados': 0, 'incidentes_encontrados': 0} | Data: {'operation': 'búsqueda de casos', 'tickets_encontrados': 0, 'incidentes_encontrados': 0}
2025-06-26 11:21:13,703 - main.flujo_completo - [32mINFO[0m - None:0 - Starting envío de reporte | Data: {'operation': 'envío de reporte', 'cedula': '1143936436', 'destinatario': '<EMAIL>'} | Data: {'operation': 'envío de reporte', 'cedula': '1143936436', 'destinatario': '<EMAIL>'}
2025-06-26 11:21:13,704 - main.flujo_completo - [32mINFO[0m - _ejecutar_envio_reporte:141 - Ejecutando comando: C:\Users\<USER>\AppData\Local\anaconda3\python.exe procesar_todo.py 1143936436 --destinatario <EMAIL>
2025-06-26 11:21:17,627 - main.flujo_completo - [31mERROR[0m - _ejecutar_envio_reporte:151 - Error al ejecutar procesar_todo.py (código 1)
2025-06-26 11:21:17,628 - main - [31mERROR[0m - main:265 - El proceso terminó con errores
2025-06-26 12:29:58,722 - main - [32mINFO[0m - None:0 - Starting aplicación principal | Data: {'operation': 'aplicación principal', 'cedula': '1143936436', 'destinatario': '<EMAIL>', 'todos': True, 'version': '1.0.0', 'debug_mode': False, 'silent_mode': False} | Data: {'operation': 'aplicación principal', 'cedula': '1143936436', 'destinatario': '<EMAIL>', 'todos': True, 'version': '1.0.0', 'debug_mode': False, 'silent_mode': False}
2025-06-26 12:29:58,723 - main.flujo_completo - [32mINFO[0m - None:0 - Starting flujo completo | Data: {'operation': 'flujo completo', 'cedula': '1143936436', 'destinatario': '<EMAIL>', 'todos': True} | Data: {'operation': 'flujo completo', 'cedula': '1143936436', 'destinatario': '<EMAIL>', 'todos': True}
2025-06-26 12:29:58,723 - main.flujo_completo - [32mINFO[0m - None:0 - Starting búsqueda de casos | Data: {'operation': 'búsqueda de casos', 'cedula': '1143936436', 'mostrar_todos': True} | Data: {'operation': 'búsqueda de casos', 'cedula': '1143936436', 'mostrar_todos': True}
2025-06-26 12:29:58,723 - main.flujo_completo - [32mINFO[0m - _ejecutar_busqueda_casos:86 - Ejecutando comando: /opt/anaconda3/bin/python open.py 1143936436 --todos
2025-06-26 12:29:58,833 - main.flujo_completo - [31mERROR[0m - _ejecutar_busqueda_casos:96 - Error al ejecutar open.py (código 1)
2025-06-26 12:29:58,834 - main.flujo_completo - [31mERROR[0m - _ejecutar_busqueda_casos:98 - Error stderr: Traceback (most recent call last):
  File "/Users/<USER>/Downloads/bmc-remedy-container/open.py", line 6, in <module>
    from prettytable import PrettyTable
ModuleNotFoundError: No module named 'prettytable'

2025-06-26 12:29:58,834 - main - [31mERROR[0m - main:265 - El proceso terminó con errores
2025-06-26 12:30:16,711 - main - [32mINFO[0m - None:0 - Starting aplicación principal | Data: {'operation': 'aplicación principal', 'cedula': '1143936436', 'destinatario': '<EMAIL>', 'todos': True, 'version': '1.0.0', 'debug_mode': False, 'silent_mode': False} | Data: {'operation': 'aplicación principal', 'cedula': '1143936436', 'destinatario': '<EMAIL>', 'todos': True, 'version': '1.0.0', 'debug_mode': False, 'silent_mode': False}
2025-06-26 12:30:16,711 - main.flujo_completo - [32mINFO[0m - None:0 - Starting flujo completo | Data: {'operation': 'flujo completo', 'cedula': '1143936436', 'destinatario': '<EMAIL>', 'todos': True} | Data: {'operation': 'flujo completo', 'cedula': '1143936436', 'destinatario': '<EMAIL>', 'todos': True}
2025-06-26 12:30:16,712 - main.flujo_completo - [32mINFO[0m - None:0 - Starting búsqueda de casos | Data: {'operation': 'búsqueda de casos', 'cedula': '1143936436', 'mostrar_todos': True} | Data: {'operation': 'búsqueda de casos', 'cedula': '1143936436', 'mostrar_todos': True}
2025-06-26 12:30:16,712 - main.flujo_completo - [32mINFO[0m - _ejecutar_busqueda_casos:86 - Ejecutando comando: /opt/anaconda3/bin/python open.py 1143936436 --todos
2025-06-26 12:30:16,820 - open.buscar_tickets - [32mINFO[0m - None:0 - Starting búsqueda de tickets | Data: {'operation': 'búsqueda de tickets', 'cedula': '1143936436', 'mostrar_todos': True} | Data: {'operation': 'búsqueda de tickets', 'cedula': '1143936436', 'mostrar_todos': True}
2025-06-26 12:30:17,363 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:78 - Buscando tickets (Work Orders) para cédula 1143936436
2025-06-26 12:30:17,762 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:181 - Se encontraron 0 work orders
2025-06-26 12:30:17,763 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:184 - Buscando incidentes para cédula 1143936436
2025-06-26 12:30:18,247 - main.flujo_completo - [32mINFO[0m - _ejecutar_busqueda_casos:103 - Resultado de la búsqueda de casos:
2025-06-26 12:30:18,247 - main.flujo_completo - [32mINFO[0m - _ejecutar_busqueda_casos:107 - 2025-06-26 12:30:16,820 - open.buscar_tickets - INFO - Starting búsqueda de tickets
2025-06-26 12:30:18,247 - main.flujo_completo - [32mINFO[0m - _ejecutar_busqueda_casos:107 - 2025-06-26 12:30:17,363 - open.buscar_tickets - INFO - Buscando tickets (Work Orders) para cédula 1143936436
2025-06-26 12:30:18,247 - main.flujo_completo - [32mINFO[0m - _ejecutar_busqueda_casos:107 - 2025-06-26 12:30:17,762 - open.buscar_tickets - INFO - Se encontraron 0 work orders
2025-06-26 12:30:18,247 - main.flujo_completo - [32mINFO[0m - _ejecutar_busqueda_casos:107 - 2025-06-26 12:30:17,763 - open.buscar_tickets - INFO - Buscando incidentes para cédula 1143936436
2025-06-26 12:30:18,247 - main.flujo_completo - [32mINFO[0m - _ejecutar_busqueda_casos:107 - Se encontraron 0 incidentes
2025-06-26 12:30:18,247 - main.flujo_completo - [32mINFO[0m - _ejecutar_busqueda_casos:107 - No se encontraron tickets ni incidentes para la cédula 1143936436
2025-06-26 12:30:18,247 - main.flujo_completo - [33mWARNING[0m - _contar_casos_encontrados:200 - Archivo de reporte no encontrado: /Users/<USER>/Downloads/bmc-remedy-container/reporte_unificado.json
2025-06-26 12:30:18,247 - main.flujo_completo - [32mINFO[0m - None:0 - Successfully completed búsqueda de casos | Data: {'operation': 'búsqueda de casos', 'tickets_encontrados': 0, 'incidentes_encontrados': 0} | Data: {'operation': 'búsqueda de casos', 'tickets_encontrados': 0, 'incidentes_encontrados': 0}
2025-06-26 12:30:18,247 - main.flujo_completo - [32mINFO[0m - None:0 - Starting envío de reporte | Data: {'operation': 'envío de reporte', 'cedula': '1143936436', 'destinatario': '<EMAIL>'} | Data: {'operation': 'envío de reporte', 'cedula': '1143936436', 'destinatario': '<EMAIL>'}
2025-06-26 12:30:18,248 - main.flujo_completo - [32mINFO[0m - _ejecutar_envio_reporte:141 - Ejecutando comando: /opt/anaconda3/bin/python procesar_todo.py 1143936436 --destinatario <EMAIL>
2025-06-26 12:30:18,328 - main.flujo_completo - [31mERROR[0m - _ejecutar_envio_reporte:151 - Error al ejecutar procesar_todo.py (código 1)
2025-06-26 12:30:18,328 - main - [31mERROR[0m - main:265 - El proceso terminó con errores
2025-06-26 12:33:49,651 - open.buscar_tickets - [32mINFO[0m - None:0 - Starting búsqueda de tickets | Data: {'operation': 'búsqueda de tickets', 'cedula': '1143936436', 'mostrar_todos': True} | Data: {'operation': 'búsqueda de tickets', 'cedula': '1143936436', 'mostrar_todos': True}
2025-06-26 12:33:50,067 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:78 - Buscando tickets (Work Orders) para cédula 1143936436
2025-06-26 12:33:50,465 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:182 - Se encontraron 0 work orders
2025-06-26 12:33:50,465 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:185 - Buscando incidentes para cédula 1143936436
2025-06-26 12:42:05,403 - open.buscar_tickets - [32mINFO[0m - None:0 - Starting búsqueda de tickets | Data: {'operation': 'búsqueda de tickets', 'cedula': '1143936436', 'mostrar_todos': True} | Data: {'operation': 'búsqueda de tickets', 'cedula': '1143936436', 'mostrar_todos': True}
2025-06-26 12:42:05,921 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:78 - Buscando tickets (Work Orders) para cédula 1143936436
2025-06-26 12:42:05,921 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:90 - URL: https://surasoporteti-qa-restapi.onbmc.com/api/arsys/v1/entry/WOI:WorkOrderInterface
2025-06-26 12:42:05,921 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:91 - Parámetros: {'q': '\'Direct Contact Corporate ID\'="1143936436"', 'limit': '1000'}
2025-06-26 12:42:05,921 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:92 - Headers: {'Authorization': 'AR-JWT eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIhPSF7ZW5jfSE9IUFBQUFETEVpaWU3NGRTcldJQ2xLWkptSGJYUDMvellONEtLWUNIY3lLUVF6M2RJRHltUmYwYTV3b1dIL1Y5a3lYbUxlZXc9PSIsIl9sb2NhbGVMYW5ndWFnZSI6ImVuIiwibmJmIjoxNzUwOTU5NjA1LCJpc3MiOiJwbGF0Zm9ybS1mdHMtMC5wbGF0Zm9ybS1mdHMiLCJfbG9jYWxlQ291bnRyeSI6IlVTIiwiX2F1dGhTdHJpbmciOiIhPSF7ZW5jfSE9IUFBQUFETEVpaWU3NGRTcldJQ2xLWlBzb013eTZyN3JYNnBxbVN3Nm9hR0U9IiwiZXhwIjoxNzUwOTg4NTI1LCJfY2FjaGVJZCI6MTk4MDU4MSwiaWF0IjoxNzUwOTU5NzI1LCJqdGkiOiJJREdIMDREWkMxVFJFQVNZN0ZHRlNZN0ZHRkY0SFEiLCJfYWJzb2x1dGVFeHBpcmF0aW9uVGltZSI6MTc1MTA0NjEyNX0.csqb3-4VYQjn1J1V0FSuRW8P7zgMwLvH7tU_UX77u0k', 'Content-Type': 'application/json', 'Cookie': 'AR-JWT=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIhPSF7ZW5jfSE9IUFBQUFETEVpaWU3NGRTcldJQ2xLWkptSGJYUDMvellONEtLWUNIY3lLUVF6M2RJRHltUmYwYTV3b1dIL1Y5a3lYbUxlZXc9PSIsIl9sb2NhbGVMYW5ndWFnZSI6ImVuIiwibmJmIjoxNzUwOTU5NjA1LCJpc3MiOiJwbGF0Zm9ybS1mdHMtMC5wbGF0Zm9ybS1mdHMiLCJfbG9jYWxlQ291bnRyeSI6IlVTIiwiX2F1dGhTdHJpbmciOiIhPSF7ZW5jfSE9IUFBQUFETEVpaWU3NGRTcldJQ2xLWlBzb013eTZyN3JYNnBxbVN3Nm9hR0U9IiwiZXhwIjoxNzUwOTg4NTI1LCJfY2FjaGVJZCI6MTk4MDU4MSwiaWF0IjoxNzUwOTU5NzI1LCJqdGkiOiJJREdIMDREWkMxVFJFQVNZN0ZHRlNZN0ZHRkY0SFEiLCJfYWJzb2x1dGVFeHBpcmF0aW9uVGltZSI6MTc1MTA0NjEyNX0.csqb3-4VYQjn1J1V0FSuRW8P7zgMwLvH7tU_UX77u0k'}
2025-06-26 12:42:06,321 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:96 - Status code work orders: 400
2025-06-26 12:42:06,321 - open.buscar_tickets - [31mERROR[0m - buscar_tickets_por_cedula:98 - Error en work orders: [{"messageType":"ERROR","messageText":"Unknown field referenced in query line","messageAppendedText":" at position 0 ( 'Direct Contact Corporate ID' )","messageNumber":1587}]
2025-06-26 12:42:06,321 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:191 - Se encontraron 0 work orders
2025-06-26 12:42:06,321 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:194 - Buscando incidentes para cédula 1143936436
2025-06-26 12:42:06,321 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:205 - URL incidentes: https://surasoporteti-qa-restapi.onbmc.com/api/arsys/v1/entry/HPD:IncidentInterface
2025-06-26 12:42:06,321 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:206 - Parámetros incidentes: {'q': '\'Direct Contact Corporate ID\'="1143936436"', 'limit': '1000'}
2025-06-26 12:42:06,804 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:210 - Status code incidentes: 200
2025-06-26 12:42:06,804 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:217 - Respuesta incidentes: {
  "entries": [],
  "numMatches": null,
  "_links": {
    "self": [
      {
        "href": "https://surasoporteti-qa-restapi.onbmc.com/api/arsys/v1/entry/HPD:IncidentInterface?q=%27Direct+Contact+Corporate+ID%27%3D%221143936436%22&limit=1000"
      }
    ]
  }
}
2025-06-26 12:42:41,633 - open.buscar_tickets - [32mINFO[0m - None:0 - Starting búsqueda de tickets | Data: {'operation': 'búsqueda de tickets', 'cedula': '1143936436', 'mostrar_todos': True} | Data: {'operation': 'búsqueda de tickets', 'cedula': '1143936436', 'mostrar_todos': True}
2025-06-26 12:42:42,038 - open.buscar_tickets - [32mINFO[0m - explorar_campos_disponibles:62 - Explorando campos disponibles...
2025-06-26 12:42:42,038 - open.buscar_tickets - [32mINFO[0m - explorar_campos_disponibles:75 - Consultando Work Orders (WOI:WorkOrderInterface)...
2025-06-26 12:42:42,499 - open.buscar_tickets - [32mINFO[0m - explorar_campos_disponibles:83 - Campos disponibles en Work Orders: ['WO Type Field 16 Label', 'Request ID', 'Submitter', 'CAB Manager Login', 'Submit Date', 'Assigned To', 'Last Modified Date', 'Status', 'Work Order Template Used', 'CAB Manager ( Change Co-ord )', 'WO Type Field 25', 'SRWorkInfoType', 'WO Type Field 21 Label', 'WO Type Field 26 Label', 'z1D_WorklogDetails', 'WO Type Field 30', 'WO Type Field 15', 'WO Type Field 10 Label', 'WO Type Field 10', 'z1D_View_Access', 'WO Type Field 20', 'Support Group ID 2', 'LookupKeyword', 'WO Type Field 11 Label', 'Assignee Groups_parent', 'z1D Action WO1', 'WO Type Field 50', 'WO Type Field 51', 'WO Type Field 48 Label', 'SRMSAOIGuid', 'WO Type Field 17 Label', 'Requested By Person ID', 'WO Type Field 22 Label', 'WO Type Field 26', 'Automation Status', 'WO Type Field 48', 'Assignee Groups', 'CI_DatasetId', 'Chat Session ID', 'z1D_ActivityDate_tab', 'WO Type Field 27 Label', 'WO Type Field 49', 'Site', 'WO Type Field 16', 'z1D_Secure_Log', 'SRInstanceID', 'WO Type Field 12 Label', 'WO Type Field 04 Label', 'WO Type Field 05 Label', 'CustomerFullName', 'WO Type Field 06 Label', 'WO Type Field 07 Label', 'WO Type Field 01 Label', 'WO Type Field 02 Label', 'WO Type Field 03 Label', 'z1D_WorkInfoViewAccess', 'WO Type Field 08 Label', 'WO Type Field 21', 'WO Type Field 09 Label', 'z1D Char09', 'WO Type Field 49 Label', 'Attachment 1', 'WO Type Field 50 Label', 'WO Type Field 51 Label', 'InstanceId', 'WO Type Field 11', 'Scheduled Start Date', 'SRMSRegistryInstanceID', 'Actual Start Date', 'z1D_ConfirmGroup', 'Requestor ID', 'WO Type Field 23 Label', 'Support Organization2', 'Support Group Name2', 'WO Type Field 28 Label', 'z1D_CommunicationSource', 'CreatedFromBackEndSynchWI', 'Broker Vendor Name', 'DWP_SRID', 'z1D_Details', 'NeedsAttentionCCS_Setting', 'WO Type Field 17', 'Scheduled End Date', 'WO Type Field 13 Label', 'DWP_SRInstanceID', 'WO Type Field 27', 'Actual End Date', 'Number of Attachments', 'WO Type Field 18 Label', 'WO Type Field 22', 'ASORG', 'ASCHG', 'ASLOGID', 'ASCPY', 'ASGRP', 'Completed Date', 'Status Reason', 'Detailed Description', 'WO Type Field 12', 'Requestor_By_ID', 'Needs Attention', 'z1D Char10', 'WO Type Field 24 Label', 'Customer Middle Name', 'Work Order ID', 'Work Order Type', 'WorkOrderID', 'WO Type Field 29 Label', 'ASGRPID', 'AttachmentSubmitter', 'Priority', 'AttachmentSourceGUID', 'Manufacturer (2)', 'WO Type Field 23', 'Product Name (2)', 'Product Model/Version (2)', 'WO Type Field 14 Label', 'WO Type Field 18', 'WO Type Field 28', 'z1D_Summary', 'WO Type Field 19 Label', 'zTmpEventGUID', 'Company3', 'Product Cat Tier 3 (2)', 'RequestCreatedFromDWP', 'CI_ReconId', 'Product Cat Tier 1(2)', 'Product Cat Tier 2 (2)', 'Customer Phone Number', 'AttachmentSourceFormName', 'Customer Last Name', 'Customer Company', 'Customer Person ID', 'Record ID', 'Customer First Name', 'Customer Internet E-mail', 'WO Type Field 13', 'Customer Organization', 'Previous_ServiceCI_ReconID', 'Customer Department', 'VIP', 'WO Type Field 25 Label', 'WO Type Field 30 Label', 'Last Name', 'First Name', 'Middle Initial', 'WO Type Field 2', 'Organization', 'WO Type Field 3', 'WO Type Field 1', 'WO Type Field 6', 'Support Organization', 'WO Type Field 7', 'Support Group Name', 'WO Type Field 4', 'WO Type Field 5', 'ClientLocale', 'WO Type Field 8', 'Description', 'WO Type Field 9', 'Location Company', 'z1D Char01', 'TemplateID', 'WO Type Field 24', 'Phone Number', 'Categorization Tier 1', 'Internet E-mail', 'WO Type Field 15 Label', 'WO Type Field 20 Label', 'Chg Location Address', 'WO Type Field 29', 'z1D_Activity Type*', 'z1D_SR_Instanceid', 'z1D Char07', 'z1D Char08', 'z1D_Command', 'Company', 'z1D Char05', 'Person ID', 'SRAttachment', 'z1D Char06', 'Add Request For:', 'z1D Action WO2', 'WO Type Field 19', 'Support Group ID', 'z1D_Action', 'z1D Integer01', 'z1D Char02', 'WO Type Field 14', 'z1D Char03', 'Categorization Tier 2', 'Categorization Tier 3', 'z1D_WorkInfoSubmitter', 'z1D Char04', 'Vendor Assignee Groups', 'Vendor Assignee Groups_parent', 'Region', 'z1D Integer02', 'z1D Integer03', 'Site Group', 'Department', 'SRID', 'z1D_Worklog Type', 'Business Service']
2025-06-26 12:42:42,499 - open.buscar_tickets - [32mINFO[0m - explorar_campos_disponibles:87 - Campos posibles para cédula en Work Orders: ['Request ID', 'Support Group ID 2', 'SRMSAOIGuid', 'Requested By Person ID', 'CI_DatasetId', 'Chat Session ID', 'SRInstanceID', 'CustomerFullName', 'InstanceId', 'SRMSRegistryInstanceID', 'Requestor ID', 'DWP_SRID', 'DWP_SRInstanceID', 'ASLOGID', 'Requestor_By_ID', 'Customer Middle Name', 'Work Order ID', 'WorkOrderID', 'ASGRPID', 'AttachmentSourceGUID', 'zTmpEventGUID', 'CI_ReconId', 'Customer Phone Number', 'Customer Last Name', 'Customer Company', 'Customer Person ID', 'Record ID', 'Customer First Name', 'Customer Internet E-mail', 'Customer Organization', 'Previous_ServiceCI_ReconID', 'Customer Department', 'Middle Initial', 'TemplateID', 'z1D_SR_Instanceid', 'Person ID', 'Support Group ID', 'SRID']
2025-06-26 12:42:42,499 - open.buscar_tickets - [32mINFO[0m - explorar_campos_disponibles:75 - Consultando Incidentes (HPD:IncidentInterface)...
2025-06-26 12:42:43,447 - open.buscar_tickets - [32mINFO[0m - explorar_campos_disponibles:83 - Campos disponibles en Incidentes: ['Request ID', 'Submitter', 'Submit Date', 'Assignee Login ID', 'Last Modified By', 'Last Modified Date', 'Status', 'policy_name', 'Created_By', 'Vendor Name', 'z1D_WorklogDetails', 'Resolution Category', 'z1D_CI_FormName', 'z1D_View_Access', 'Owner Support Company', 'Owner Group ID', 'Owner Group', 'Assignee Groups_parent', 'Auto Open Session', 'Impact_OR_Root', 'ServiceCI', 'HPD_CI_ReconID', 'ServiceCI_ReconID', 'Additional Access', 'Additional Access Parent', 'SRMSAOIGuid', 'Assignee Groups', 'Chat Session ID', 'z1D_ActivityDate_tab', 'HPD_CI_FormName', 'Site', 'Modified Chat Session ID', 'z1D_Secure_Log', 'COG_CognSuppGrpComp', 'Direct Contact Site ID', 'Associated Alarm', 'SRInstanceID', 'Direct Contact Mail Station', 'Direct Contact Location Details', 'Direct Contact Local Number', 'Direct Contact Extension', 'COG_CognSuppGrpOrg', 'Direct Contact Country Code', 'InfrastructureEventType', 'Direct Contact Area Code', 'COG_CognSuppGrpName', 'Direct Contact Country', 'Direct Contact State/Province', 'PortNumber', 'Direct Contact Street', 'Direct Contact Time Zone', 'Direct Contact Desk Location', 'Direct Contact City', 'FirstWIPDate', 'Direct Contact Zip/Postal Code', 'LastWIPDate', 'COG_CognSuppGrpID', 'Closure Manufacturer', 'z2AF_Act_Attachment_1', 'z1D_COG_AutoSuppGrpPredRule', 'Resolution Category Tier 3', 'Closure Product Category Tier1', 'Resolution Method', 'Resolution Category Tier 2', 'Closure Product Name', 'status_reason2', 'Closure Product Model/Version', 'Closure Product Category Tier2', 'HPD_CI', 'Closure Product Category Tier3', 'Assigned Group Shift Name', 'InstanceId', 'Assigned Group Shift ID', 'SRMS Registry Instance ID', 'z1D_ConfirmGroup', 'Owner Support Organization', 'z1D_CommunicationSource', 'z1D_CreatedFromBackEndSynchWI', 'TimeOfEvent', 'Broker Vendor Name', 'DWP_SRID', 'z1D_Details', 'NeedsAttentionCCS_Setting', 'z1D_Char02', 'DWP_SRInstanceID', 'AppPassword', 'Number of Attachments', 'cell_name', 'Direct Contact Organization', 'Direct Contact Department', 'Direct Contact Middle Initial', 'Direct Contact Phone Number', 'Direct Contact Site', 'Direct Contact Person ID', 'Resolution', 'Direct Contact Region', 'Customer Login ID', 'Direct Contact Site Group', 'Status_Reason', 'Direct Contact Last Name', 'Detailed Decription', 'Direct Contact First Name', 'Direct Contact Company', 'status_incident', 'policy_type', 'Needs Attention', 'Vendor Ticket Number', 'Estimated Resolution Date', 'root_incident_id_list', 'AppInterfaceForm', 'Total Transfers', 'Protocol', 'Priority Weight', 'Urgency', 'Direct Contact Login ID', 'Impact', 'Incident Number', 'Priority', 'mc_ueid', 'Assignee', 'AppInstanceServer', 'AttachmentSourceGUID', 'Assigned Group', 'Reported Source', 'Last _Assigned_Date', 'z1D_AssociationDescription', 'z1D_Summary', 'zTmpEventGUID', 'Assigned Support Company', 'RequestCreatedFromDWP', 'z1D_SV_AlarmId', 'AppLogin', 'AttachmentSourceFormName', 'Record ID', 'Direct Contact Internet E-mail', 'Previous_ServiceCI_ReconID', 'VIP', 'Contact Sensitivity', 'use_case', 'Required Resolution DateTime', 'Last Name', 'First Name', 'Component_ID', 'Contact Client Type', 'Middle Initial', 'Organization', 'AccessMode', 'TemplateID', 'Assigned Support Organization', 'ClientLocale', 'Country', 'State Province', 'Description', 'Company', 'z1D_COG_SuppGrpWorkInfoTag', 'City', 'z1D Char01', 'Phone Number', 'Product Model/Version', 'Product Name', 'Manufacturer', 'Categorization Tier 1', 'Last Acknowledged Date', 'Last Resolved Date', 'Internet E-mail', 'Reported Date', 'Responded Date', 'Corporate ID', 'Closed Date', 'z1D Permission Group ID', 'MaxRetries', 'z1D Permission Group List', 'Desk Location', 'Zip/Postal Code', 'z1D_Activity_Type', 'Mail Station', 'z1D_SR_Instanceid', 'Street', 'z1D_InterfaceAction', 'z1D_Command', 'KMSGUID', 'Contact Company', 'Person ID', 'Vendor Group ID', 'SRAttachment', 'Site ID', 'z1D_FormName', 'Vendor Organization', 'Assigned Group ID', 'Vendor Group', 'z1D Action', 'z1D Integer01', 'z1D Char02', 'z1D Char03', 'Direct Contact Corporate ID', 'Categorization Tier 2', 'Previous_HPD_CI_ReconID', 'Categorization Tier 3', 'bOrphanedRoot', 'z1D_WorkInfoSubmitter', 'z1D Char04', 'root_component_id_list', 'Vendor Assignee Groups', 'Vendor Assignee Groups_parent', 'Region', 'Product Categorization Tier 1', 'z1D Integer02', 'Site Group', 'Department', 'Product Categorization Tier 3', 'Product Categorization Tier 2', 'Entry ID', 'z1D Modify All Flag-V', 'Service Type', 'z1D Char27', 'SRID']
2025-06-26 12:42:43,448 - open.buscar_tickets - [32mINFO[0m - explorar_campos_disponibles:87 - Campos posibles para cédula en Incidentes: ['Request ID', 'Assignee Login ID', 'Owner Group ID', 'HPD_CI_ReconID', 'ServiceCI_ReconID', 'SRMSAOIGuid', 'Chat Session ID', 'Modified Chat Session ID', 'Direct Contact Site ID', 'SRInstanceID', 'Direct Contact Mail Station', 'Direct Contact Location Details', 'Direct Contact Local Number', 'Direct Contact Extension', 'Direct Contact Country Code', 'Direct Contact Area Code', 'Direct Contact Country', 'Direct Contact State/Province', 'Direct Contact Street', 'Direct Contact Time Zone', 'Direct Contact Desk Location', 'Direct Contact City', 'Direct Contact Zip/Postal Code', 'COG_CognSuppGrpID', 'InstanceId', 'Assigned Group Shift ID', 'SRMS Registry Instance ID', 'DWP_SRID', 'DWP_SRInstanceID', 'Direct Contact Organization', 'Direct Contact Department', 'Direct Contact Middle Initial', 'Direct Contact Phone Number', 'Direct Contact Site', 'Direct Contact Person ID', 'Direct Contact Region', 'Customer Login ID', 'Direct Contact Site Group', 'Direct Contact Last Name', 'Direct Contact First Name', 'Direct Contact Company', 'status_incident', 'root_incident_id_list', 'Direct Contact Login ID', 'Incident Number', 'mc_ueid', 'AttachmentSourceGUID', 'zTmpEventGUID', 'z1D_SV_AlarmId', 'Record ID', 'Direct Contact Internet E-mail', 'Previous_ServiceCI_ReconID', 'Contact Sensitivity', 'Component_ID', 'Contact Client Type', 'Middle Initial', 'TemplateID', 'Corporate ID', 'z1D Permission Group ID', 'z1D_SR_Instanceid', 'KMSGUID', 'Contact Company', 'Person ID', 'Vendor Group ID', 'Site ID', 'Assigned Group ID', 'Direct Contact Corporate ID', 'Previous_HPD_CI_ReconID', 'root_component_id_list', 'Entry ID', 'SRID']
2025-06-26 12:42:43,449 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:119 - Buscando tickets (Work Orders) para cédula 1143936436
2025-06-26 12:42:43,449 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:131 - URL: https://surasoporteti-qa-restapi.onbmc.com/api/arsys/v1/entry/WOI:WorkOrderInterface
2025-06-26 12:42:43,449 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:132 - Parámetros: {'q': '\'Direct Contact Corporate ID\'="1143936436"', 'limit': '1000'}
2025-06-26 12:42:43,449 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:133 - Headers: {'Authorization': 'AR-JWT eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIhPSF7ZW5jfSE9IUFBQUFETEVpaWU3NGRTcldJQ2xLWkptSGJYUDMvellONEtLWUNIY3lLUVF6M2RJRHltUmYwYTV3b1dIL1Y5a3lYbUxlZXc9PSIsIl9sb2NhbGVMYW5ndWFnZSI6ImVuIiwibmJmIjoxNzUwOTU5NjQyLCJpc3MiOiJwbGF0Zm9ybS1mdHMtMC5wbGF0Zm9ybS1mdHMiLCJfbG9jYWxlQ291bnRyeSI6IlVTIiwiX2F1dGhTdHJpbmciOiIhPSF7ZW5jfSE9IUFBQUFETEVpaWU3NGRTcldJQ2xLWlBzb013eTZyN3JYNnBxbVN3Nm9hR0U9IiwiZXhwIjoxNzUwOTg4NTYyLCJfY2FjaGVJZCI6MTk4MDcyOCwiaWF0IjoxNzUwOTU5NzYyLCJqdGkiOiJJREdIMDREWkMxVFJFQVNZN0ZIR1NZN0ZIR0Y0OFEiLCJfYWJzb2x1dGVFeHBpcmF0aW9uVGltZSI6MTc1MTA0NjE2Mn0.XDW0jhHWeVpFC96qz5GLhgTstCEzOIq7foCmXatrl6s', 'Content-Type': 'application/json', 'Cookie': 'AR-JWT=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIhPSF7ZW5jfSE9IUFBQUFETEVpaWU3NGRTcldJQ2xLWkptSGJYUDMvellONEtLWUNIY3lLUVF6M2RJRHltUmYwYTV3b1dIL1Y5a3lYbUxlZXc9PSIsIl9sb2NhbGVMYW5ndWFnZSI6ImVuIiwibmJmIjoxNzUwOTU5NjQyLCJpc3MiOiJwbGF0Zm9ybS1mdHMtMC5wbGF0Zm9ybS1mdHMiLCJfbG9jYWxlQ291bnRyeSI6IlVTIiwiX2F1dGhTdHJpbmciOiIhPSF7ZW5jfSE9IUFBQUFETEVpaWU3NGRTcldJQ2xLWlBzb013eTZyN3JYNnBxbVN3Nm9hR0U9IiwiZXhwIjoxNzUwOTg4NTYyLCJfY2FjaGVJZCI6MTk4MDcyOCwiaWF0IjoxNzUwOTU5NzYyLCJqdGkiOiJJREdIMDREWkMxVFJFQVNZN0ZIR1NZN0ZIR0Y0OFEiLCJfYWJzb2x1dGVFeHBpcmF0aW9uVGltZSI6MTc1MTA0NjE2Mn0.XDW0jhHWeVpFC96qz5GLhgTstCEzOIq7foCmXatrl6s'}
2025-06-26 12:42:43,857 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:137 - Status code work orders: 400
2025-06-26 12:42:43,858 - open.buscar_tickets - [31mERROR[0m - buscar_tickets_por_cedula:139 - Error en work orders: [{"messageType":"ERROR","messageText":"Unknown field referenced in query line","messageAppendedText":" at position 0 ( 'Direct Contact Corporate ID' )","messageNumber":1587}]
2025-06-26 12:42:43,858 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:232 - Se encontraron 0 work orders
2025-06-26 12:42:43,858 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:235 - Buscando incidentes para cédula 1143936436
2025-06-26 12:42:43,858 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:246 - URL incidentes: https://surasoporteti-qa-restapi.onbmc.com/api/arsys/v1/entry/HPD:IncidentInterface
2025-06-26 12:42:43,858 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:247 - Parámetros incidentes: {'q': '\'Direct Contact Corporate ID\'="1143936436"', 'limit': '1000'}
2025-06-26 12:42:44,297 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:251 - Status code incidentes: 200
2025-06-26 12:42:44,298 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:258 - Respuesta incidentes: {
  "entries": [],
  "numMatches": null,
  "_links": {
    "self": [
      {
        "href": "https://surasoporteti-qa-restapi.onbmc.com/api/arsys/v1/entry/HPD:IncidentInterface?q=%27Direct+Contact+Corporate+ID%27%3D%221143936436%22&limit=1000"
      }
    ]
  }
}
2025-06-26 12:43:11,378 - open.buscar_tickets - [32mINFO[0m - None:0 - Starting búsqueda de tickets | Data: {'operation': 'búsqueda de tickets', 'cedula': '1143936436', 'mostrar_todos': True} | Data: {'operation': 'búsqueda de tickets', 'cedula': '1143936436', 'mostrar_todos': True}
2025-06-26 12:43:11,783 - open.buscar_tickets - [32mINFO[0m - explorar_campos_disponibles:62 - Explorando campos disponibles...
2025-06-26 12:43:11,783 - open.buscar_tickets - [32mINFO[0m - explorar_campos_disponibles:75 - Consultando Work Orders (WOI:WorkOrderInterface)...
2025-06-26 12:43:12,237 - open.buscar_tickets - [32mINFO[0m - explorar_campos_disponibles:83 - Campos disponibles en Work Orders: ['WO Type Field 16 Label', 'Request ID', 'Submitter', 'CAB Manager Login', 'Submit Date', 'Assigned To', 'Last Modified Date', 'Status', 'Work Order Template Used', 'CAB Manager ( Change Co-ord )', 'WO Type Field 25', 'SRWorkInfoType', 'WO Type Field 21 Label', 'WO Type Field 26 Label', 'z1D_WorklogDetails', 'WO Type Field 30', 'WO Type Field 15', 'WO Type Field 10 Label', 'WO Type Field 10', 'z1D_View_Access', 'WO Type Field 20', 'Support Group ID 2', 'LookupKeyword', 'WO Type Field 11 Label', 'Assignee Groups_parent', 'z1D Action WO1', 'WO Type Field 50', 'WO Type Field 51', 'WO Type Field 48 Label', 'SRMSAOIGuid', 'WO Type Field 17 Label', 'Requested By Person ID', 'WO Type Field 22 Label', 'WO Type Field 26', 'Automation Status', 'WO Type Field 48', 'Assignee Groups', 'CI_DatasetId', 'Chat Session ID', 'z1D_ActivityDate_tab', 'WO Type Field 27 Label', 'WO Type Field 49', 'Site', 'WO Type Field 16', 'z1D_Secure_Log', 'SRInstanceID', 'WO Type Field 12 Label', 'WO Type Field 04 Label', 'WO Type Field 05 Label', 'CustomerFullName', 'WO Type Field 06 Label', 'WO Type Field 07 Label', 'WO Type Field 01 Label', 'WO Type Field 02 Label', 'WO Type Field 03 Label', 'z1D_WorkInfoViewAccess', 'WO Type Field 08 Label', 'WO Type Field 21', 'WO Type Field 09 Label', 'z1D Char09', 'WO Type Field 49 Label', 'Attachment 1', 'WO Type Field 50 Label', 'WO Type Field 51 Label', 'InstanceId', 'WO Type Field 11', 'Scheduled Start Date', 'SRMSRegistryInstanceID', 'Actual Start Date', 'z1D_ConfirmGroup', 'Requestor ID', 'WO Type Field 23 Label', 'Support Organization2', 'Support Group Name2', 'WO Type Field 28 Label', 'z1D_CommunicationSource', 'CreatedFromBackEndSynchWI', 'Broker Vendor Name', 'DWP_SRID', 'z1D_Details', 'NeedsAttentionCCS_Setting', 'WO Type Field 17', 'Scheduled End Date', 'WO Type Field 13 Label', 'DWP_SRInstanceID', 'WO Type Field 27', 'Actual End Date', 'Number of Attachments', 'WO Type Field 18 Label', 'WO Type Field 22', 'ASORG', 'ASCHG', 'ASLOGID', 'ASCPY', 'ASGRP', 'Completed Date', 'Status Reason', 'Detailed Description', 'WO Type Field 12', 'Requestor_By_ID', 'Needs Attention', 'z1D Char10', 'WO Type Field 24 Label', 'Customer Middle Name', 'Work Order ID', 'Work Order Type', 'WorkOrderID', 'WO Type Field 29 Label', 'ASGRPID', 'AttachmentSubmitter', 'Priority', 'AttachmentSourceGUID', 'Manufacturer (2)', 'WO Type Field 23', 'Product Name (2)', 'Product Model/Version (2)', 'WO Type Field 14 Label', 'WO Type Field 18', 'WO Type Field 28', 'z1D_Summary', 'WO Type Field 19 Label', 'zTmpEventGUID', 'Company3', 'Product Cat Tier 3 (2)', 'RequestCreatedFromDWP', 'CI_ReconId', 'Product Cat Tier 1(2)', 'Product Cat Tier 2 (2)', 'Customer Phone Number', 'AttachmentSourceFormName', 'Customer Last Name', 'Customer Company', 'Customer Person ID', 'Record ID', 'Customer First Name', 'Customer Internet E-mail', 'WO Type Field 13', 'Customer Organization', 'Previous_ServiceCI_ReconID', 'Customer Department', 'VIP', 'WO Type Field 25 Label', 'WO Type Field 30 Label', 'Last Name', 'First Name', 'Middle Initial', 'WO Type Field 2', 'Organization', 'WO Type Field 3', 'WO Type Field 1', 'WO Type Field 6', 'Support Organization', 'WO Type Field 7', 'Support Group Name', 'WO Type Field 4', 'WO Type Field 5', 'ClientLocale', 'WO Type Field 8', 'Description', 'WO Type Field 9', 'Location Company', 'z1D Char01', 'TemplateID', 'WO Type Field 24', 'Phone Number', 'Categorization Tier 1', 'Internet E-mail', 'WO Type Field 15 Label', 'WO Type Field 20 Label', 'Chg Location Address', 'WO Type Field 29', 'z1D_Activity Type*', 'z1D_SR_Instanceid', 'z1D Char07', 'z1D Char08', 'z1D_Command', 'Company', 'z1D Char05', 'Person ID', 'SRAttachment', 'z1D Char06', 'Add Request For:', 'z1D Action WO2', 'WO Type Field 19', 'Support Group ID', 'z1D_Action', 'z1D Integer01', 'z1D Char02', 'WO Type Field 14', 'z1D Char03', 'Categorization Tier 2', 'Categorization Tier 3', 'z1D_WorkInfoSubmitter', 'z1D Char04', 'Vendor Assignee Groups', 'Vendor Assignee Groups_parent', 'Region', 'z1D Integer02', 'z1D Integer03', 'Site Group', 'Department', 'SRID', 'z1D_Worklog Type', 'Business Service']
2025-06-26 12:43:12,237 - open.buscar_tickets - [32mINFO[0m - explorar_campos_disponibles:87 - Campos posibles para cédula en Work Orders: ['Request ID', 'Support Group ID 2', 'SRMSAOIGuid', 'Requested By Person ID', 'CI_DatasetId', 'Chat Session ID', 'SRInstanceID', 'CustomerFullName', 'InstanceId', 'SRMSRegistryInstanceID', 'Requestor ID', 'DWP_SRID', 'DWP_SRInstanceID', 'ASLOGID', 'Requestor_By_ID', 'Customer Middle Name', 'Work Order ID', 'WorkOrderID', 'ASGRPID', 'AttachmentSourceGUID', 'zTmpEventGUID', 'CI_ReconId', 'Customer Phone Number', 'Customer Last Name', 'Customer Company', 'Customer Person ID', 'Record ID', 'Customer First Name', 'Customer Internet E-mail', 'Customer Organization', 'Previous_ServiceCI_ReconID', 'Customer Department', 'Middle Initial', 'TemplateID', 'z1D_SR_Instanceid', 'Person ID', 'Support Group ID', 'SRID']
2025-06-26 12:43:12,237 - open.buscar_tickets - [32mINFO[0m - explorar_campos_disponibles:75 - Consultando Incidentes (HPD:IncidentInterface)...
2025-06-26 12:43:13,287 - open.buscar_tickets - [32mINFO[0m - explorar_campos_disponibles:83 - Campos disponibles en Incidentes: ['Request ID', 'Submitter', 'Submit Date', 'Assignee Login ID', 'Last Modified By', 'Last Modified Date', 'Status', 'policy_name', 'Created_By', 'Vendor Name', 'z1D_WorklogDetails', 'Resolution Category', 'z1D_CI_FormName', 'z1D_View_Access', 'Owner Support Company', 'Owner Group ID', 'Owner Group', 'Assignee Groups_parent', 'Auto Open Session', 'Impact_OR_Root', 'ServiceCI', 'HPD_CI_ReconID', 'ServiceCI_ReconID', 'Additional Access', 'Additional Access Parent', 'SRMSAOIGuid', 'Assignee Groups', 'Chat Session ID', 'z1D_ActivityDate_tab', 'HPD_CI_FormName', 'Site', 'Modified Chat Session ID', 'z1D_Secure_Log', 'COG_CognSuppGrpComp', 'Direct Contact Site ID', 'Associated Alarm', 'SRInstanceID', 'Direct Contact Mail Station', 'Direct Contact Location Details', 'Direct Contact Local Number', 'Direct Contact Extension', 'COG_CognSuppGrpOrg', 'Direct Contact Country Code', 'InfrastructureEventType', 'Direct Contact Area Code', 'COG_CognSuppGrpName', 'Direct Contact Country', 'Direct Contact State/Province', 'PortNumber', 'Direct Contact Street', 'Direct Contact Time Zone', 'Direct Contact Desk Location', 'Direct Contact City', 'FirstWIPDate', 'Direct Contact Zip/Postal Code', 'LastWIPDate', 'COG_CognSuppGrpID', 'Closure Manufacturer', 'z2AF_Act_Attachment_1', 'z1D_COG_AutoSuppGrpPredRule', 'Resolution Category Tier 3', 'Closure Product Category Tier1', 'Resolution Method', 'Resolution Category Tier 2', 'Closure Product Name', 'status_reason2', 'Closure Product Model/Version', 'Closure Product Category Tier2', 'HPD_CI', 'Closure Product Category Tier3', 'Assigned Group Shift Name', 'InstanceId', 'Assigned Group Shift ID', 'SRMS Registry Instance ID', 'z1D_ConfirmGroup', 'Owner Support Organization', 'z1D_CommunicationSource', 'z1D_CreatedFromBackEndSynchWI', 'TimeOfEvent', 'Broker Vendor Name', 'DWP_SRID', 'z1D_Details', 'NeedsAttentionCCS_Setting', 'z1D_Char02', 'DWP_SRInstanceID', 'AppPassword', 'Number of Attachments', 'cell_name', 'Direct Contact Organization', 'Direct Contact Department', 'Direct Contact Middle Initial', 'Direct Contact Phone Number', 'Direct Contact Site', 'Direct Contact Person ID', 'Resolution', 'Direct Contact Region', 'Customer Login ID', 'Direct Contact Site Group', 'Status_Reason', 'Direct Contact Last Name', 'Detailed Decription', 'Direct Contact First Name', 'Direct Contact Company', 'status_incident', 'policy_type', 'Needs Attention', 'Vendor Ticket Number', 'Estimated Resolution Date', 'root_incident_id_list', 'AppInterfaceForm', 'Total Transfers', 'Protocol', 'Priority Weight', 'Urgency', 'Direct Contact Login ID', 'Impact', 'Incident Number', 'Priority', 'mc_ueid', 'Assignee', 'AppInstanceServer', 'AttachmentSourceGUID', 'Assigned Group', 'Reported Source', 'Last _Assigned_Date', 'z1D_AssociationDescription', 'z1D_Summary', 'zTmpEventGUID', 'Assigned Support Company', 'RequestCreatedFromDWP', 'z1D_SV_AlarmId', 'AppLogin', 'AttachmentSourceFormName', 'Record ID', 'Direct Contact Internet E-mail', 'Previous_ServiceCI_ReconID', 'VIP', 'Contact Sensitivity', 'use_case', 'Required Resolution DateTime', 'Last Name', 'First Name', 'Component_ID', 'Contact Client Type', 'Middle Initial', 'Organization', 'AccessMode', 'TemplateID', 'Assigned Support Organization', 'ClientLocale', 'Country', 'State Province', 'Description', 'Company', 'z1D_COG_SuppGrpWorkInfoTag', 'City', 'z1D Char01', 'Phone Number', 'Product Model/Version', 'Product Name', 'Manufacturer', 'Categorization Tier 1', 'Last Acknowledged Date', 'Last Resolved Date', 'Internet E-mail', 'Reported Date', 'Responded Date', 'Corporate ID', 'Closed Date', 'z1D Permission Group ID', 'MaxRetries', 'z1D Permission Group List', 'Desk Location', 'Zip/Postal Code', 'z1D_Activity_Type', 'Mail Station', 'z1D_SR_Instanceid', 'Street', 'z1D_InterfaceAction', 'z1D_Command', 'KMSGUID', 'Contact Company', 'Person ID', 'Vendor Group ID', 'SRAttachment', 'Site ID', 'z1D_FormName', 'Vendor Organization', 'Assigned Group ID', 'Vendor Group', 'z1D Action', 'z1D Integer01', 'z1D Char02', 'z1D Char03', 'Direct Contact Corporate ID', 'Categorization Tier 2', 'Previous_HPD_CI_ReconID', 'Categorization Tier 3', 'bOrphanedRoot', 'z1D_WorkInfoSubmitter', 'z1D Char04', 'root_component_id_list', 'Vendor Assignee Groups', 'Vendor Assignee Groups_parent', 'Region', 'Product Categorization Tier 1', 'z1D Integer02', 'Site Group', 'Department', 'Product Categorization Tier 3', 'Product Categorization Tier 2', 'Entry ID', 'z1D Modify All Flag-V', 'Service Type', 'z1D Char27', 'SRID']
2025-06-26 12:43:13,287 - open.buscar_tickets - [32mINFO[0m - explorar_campos_disponibles:87 - Campos posibles para cédula en Incidentes: ['Request ID', 'Assignee Login ID', 'Owner Group ID', 'HPD_CI_ReconID', 'ServiceCI_ReconID', 'SRMSAOIGuid', 'Chat Session ID', 'Modified Chat Session ID', 'Direct Contact Site ID', 'SRInstanceID', 'Direct Contact Mail Station', 'Direct Contact Location Details', 'Direct Contact Local Number', 'Direct Contact Extension', 'Direct Contact Country Code', 'Direct Contact Area Code', 'Direct Contact Country', 'Direct Contact State/Province', 'Direct Contact Street', 'Direct Contact Time Zone', 'Direct Contact Desk Location', 'Direct Contact City', 'Direct Contact Zip/Postal Code', 'COG_CognSuppGrpID', 'InstanceId', 'Assigned Group Shift ID', 'SRMS Registry Instance ID', 'DWP_SRID', 'DWP_SRInstanceID', 'Direct Contact Organization', 'Direct Contact Department', 'Direct Contact Middle Initial', 'Direct Contact Phone Number', 'Direct Contact Site', 'Direct Contact Person ID', 'Direct Contact Region', 'Customer Login ID', 'Direct Contact Site Group', 'Direct Contact Last Name', 'Direct Contact First Name', 'Direct Contact Company', 'status_incident', 'root_incident_id_list', 'Direct Contact Login ID', 'Incident Number', 'mc_ueid', 'AttachmentSourceGUID', 'zTmpEventGUID', 'z1D_SV_AlarmId', 'Record ID', 'Direct Contact Internet E-mail', 'Previous_ServiceCI_ReconID', 'Contact Sensitivity', 'Component_ID', 'Contact Client Type', 'Middle Initial', 'TemplateID', 'Corporate ID', 'z1D Permission Group ID', 'z1D_SR_Instanceid', 'KMSGUID', 'Contact Company', 'Person ID', 'Vendor Group ID', 'Site ID', 'Assigned Group ID', 'Direct Contact Corporate ID', 'Previous_HPD_CI_ReconID', 'root_component_id_list', 'Entry ID', 'SRID']
2025-06-26 12:43:13,287 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:119 - Buscando tickets (Work Orders) para cédula 1143936436
2025-06-26 12:43:13,288 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:149 - URL: https://surasoporteti-qa-restapi.onbmc.com/api/arsys/v1/entry/WOI:WorkOrderInterface
2025-06-26 12:43:13,288 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:150 - Parámetros: {'q': '(\'Customer Person ID\'="1143936436" OR \'Person ID\'="1143936436" OR \'Requestor ID\'="1143936436" OR \'Customer Company\'="1143936436" OR \'Customer First Name\'="1143936436" OR \'Customer Last Name\'="1143936436")', 'limit': '1000'}
2025-06-26 12:43:13,288 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:151 - Headers: {'Authorization': 'AR-JWT eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIhPSF7ZW5jfSE9IUFBQUFETEVpaWU3NGRTcldJQ2xLWkptSGJYUDMvellONEtLWUNIY3lLUVF6M2RJRHltUmYwYTV3b1dIL1Y5a3lYbUxlZXc9PSIsIl9sb2NhbGVMYW5ndWFnZSI6ImVuIiwibmJmIjoxNzUwOTU5NjcxLCJpc3MiOiJwbGF0Zm9ybS1mdHMtMC5wbGF0Zm9ybS1mdHMiLCJfbG9jYWxlQ291bnRyeSI6IlVTIiwiX2F1dGhTdHJpbmciOiIhPSF7ZW5jfSE9IUFBQUFETEVpaWU3NGRTcldJQ2xLWlBzb013eTZyN3JYNnBxbVN3Nm9hR0U9IiwiZXhwIjoxNzUwOTg4NTkxLCJfY2FjaGVJZCI6MTk4MDc1MywiaWF0IjoxNzUwOTU5NzkxLCJqdGkiOiJJREdIMDREWkMxVFJFQVNZN0ZIWlNZN0ZIWkY0U08iLCJfYWJzb2x1dGVFeHBpcmF0aW9uVGltZSI6MTc1MTA0NjE5MX0.y7D8jb5x2pmHSulsjHvSwWHU2hQaU-BBLKK3vgrUhoA', 'Content-Type': 'application/json', 'Cookie': 'AR-JWT=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIhPSF7ZW5jfSE9IUFBQUFETEVpaWU3NGRTcldJQ2xLWkptSGJYUDMvellONEtLWUNIY3lLUVF6M2RJRHltUmYwYTV3b1dIL1Y5a3lYbUxlZXc9PSIsIl9sb2NhbGVMYW5ndWFnZSI6ImVuIiwibmJmIjoxNzUwOTU5NjcxLCJpc3MiOiJwbGF0Zm9ybS1mdHMtMC5wbGF0Zm9ybS1mdHMiLCJfbG9jYWxlQ291bnRyeSI6IlVTIiwiX2F1dGhTdHJpbmciOiIhPSF7ZW5jfSE9IUFBQUFETEVpaWU3NGRTcldJQ2xLWlBzb013eTZyN3JYNnBxbVN3Nm9hR0U9IiwiZXhwIjoxNzUwOTg4NTkxLCJfY2FjaGVJZCI6MTk4MDc1MywiaWF0IjoxNzUwOTU5NzkxLCJqdGkiOiJJREdIMDREWkMxVFJFQVNZN0ZIWlNZN0ZIWkY0U08iLCJfYWJzb2x1dGVFeHBpcmF0aW9uVGltZSI6MTc1MTA0NjE5MX0.y7D8jb5x2pmHSulsjHvSwWHU2hQaU-BBLKK3vgrUhoA'}
2025-06-26 12:43:13,693 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:155 - Status code work orders: 200
2025-06-26 12:43:13,693 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:162 - Respuesta work orders: {
  "entries": [],
  "numMatches": null,
  "_links": {
    "self": [
      {
        "href": "https://surasoporteti-qa-restapi.onbmc.com/api/arsys/v1/entry/WOI:WorkOrderInterface?q=%28%27Customer+Person+ID%27%3D%221143936436%22+OR+%27Person+ID%27%3D%221143936436%22+OR+%27Requestor+ID%27%3D%221143936436%22+OR+%27Customer+Company%27%3D%221143936436%22+OR+%27Customer+First+Name%27%3D%221143936436%22+OR+%27Customer+Last+Name%27%3D%221143936436%22%29&limit=1000"
      }
    ]
  }
}
2025-06-26 12:43:13,693 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:250 - Se encontraron 0 work orders
2025-06-26 12:43:13,694 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:253 - Buscando incidentes para cédula 1143936436
2025-06-26 12:43:13,694 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:264 - URL incidentes: https://surasoporteti-qa-restapi.onbmc.com/api/arsys/v1/entry/HPD:IncidentInterface
2025-06-26 12:43:13,694 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:265 - Parámetros incidentes: {'q': '\'Direct Contact Corporate ID\'="1143936436"', 'limit': '1000'}
2025-06-26 12:43:14,139 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:269 - Status code incidentes: 200
2025-06-26 12:43:14,139 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:276 - Respuesta incidentes: {
  "entries": [],
  "numMatches": null,
  "_links": {
    "self": [
      {
        "href": "https://surasoporteti-qa-restapi.onbmc.com/api/arsys/v1/entry/HPD:IncidentInterface?q=%27Direct+Contact+Corporate+ID%27%3D%221143936436%22&limit=1000"
      }
    ]
  }
}
2025-06-26 12:43:54,760 - open.buscar_tickets - [32mINFO[0m - None:0 - Starting búsqueda de tickets | Data: {'operation': 'búsqueda de tickets', 'cedula': '1143936436', 'mostrar_todos': True} | Data: {'operation': 'búsqueda de tickets', 'cedula': '1143936436', 'mostrar_todos': True}
2025-06-26 12:43:55,274 - open.buscar_tickets - [32mINFO[0m - explorar_campos_disponibles:110 - Explorando campos disponibles...
2025-06-26 12:43:55,274 - open.buscar_tickets - [32mINFO[0m - explorar_campos_disponibles:123 - Consultando Work Orders (WOI:WorkOrderInterface)...
2025-06-26 12:43:55,730 - open.buscar_tickets - [32mINFO[0m - explorar_campos_disponibles:131 - Campos disponibles en Work Orders: ['WO Type Field 16 Label', 'Request ID', 'Submitter', 'CAB Manager Login', 'Submit Date', 'Assigned To', 'Last Modified Date', 'Status', 'Work Order Template Used', 'CAB Manager ( Change Co-ord )', 'WO Type Field 25', 'SRWorkInfoType', 'WO Type Field 21 Label', 'WO Type Field 26 Label', 'z1D_WorklogDetails', 'WO Type Field 30', 'WO Type Field 15', 'WO Type Field 10 Label', 'WO Type Field 10', 'z1D_View_Access', 'WO Type Field 20', 'Support Group ID 2', 'LookupKeyword', 'WO Type Field 11 Label', 'Assignee Groups_parent', 'z1D Action WO1', 'WO Type Field 50', 'WO Type Field 51', 'WO Type Field 48 Label', 'SRMSAOIGuid', 'WO Type Field 17 Label', 'Requested By Person ID', 'WO Type Field 22 Label', 'WO Type Field 26', 'Automation Status', 'WO Type Field 48', 'Assignee Groups', 'CI_DatasetId', 'Chat Session ID', 'z1D_ActivityDate_tab', 'WO Type Field 27 Label', 'WO Type Field 49', 'Site', 'WO Type Field 16', 'z1D_Secure_Log', 'SRInstanceID', 'WO Type Field 12 Label', 'WO Type Field 04 Label', 'WO Type Field 05 Label', 'CustomerFullName', 'WO Type Field 06 Label', 'WO Type Field 07 Label', 'WO Type Field 01 Label', 'WO Type Field 02 Label', 'WO Type Field 03 Label', 'z1D_WorkInfoViewAccess', 'WO Type Field 08 Label', 'WO Type Field 21', 'WO Type Field 09 Label', 'z1D Char09', 'WO Type Field 49 Label', 'Attachment 1', 'WO Type Field 50 Label', 'WO Type Field 51 Label', 'InstanceId', 'WO Type Field 11', 'Scheduled Start Date', 'SRMSRegistryInstanceID', 'Actual Start Date', 'z1D_ConfirmGroup', 'Requestor ID', 'WO Type Field 23 Label', 'Support Organization2', 'Support Group Name2', 'WO Type Field 28 Label', 'z1D_CommunicationSource', 'CreatedFromBackEndSynchWI', 'Broker Vendor Name', 'DWP_SRID', 'z1D_Details', 'NeedsAttentionCCS_Setting', 'WO Type Field 17', 'Scheduled End Date', 'WO Type Field 13 Label', 'DWP_SRInstanceID', 'WO Type Field 27', 'Actual End Date', 'Number of Attachments', 'WO Type Field 18 Label', 'WO Type Field 22', 'ASORG', 'ASCHG', 'ASLOGID', 'ASCPY', 'ASGRP', 'Completed Date', 'Status Reason', 'Detailed Description', 'WO Type Field 12', 'Requestor_By_ID', 'Needs Attention', 'z1D Char10', 'WO Type Field 24 Label', 'Customer Middle Name', 'Work Order ID', 'Work Order Type', 'WorkOrderID', 'WO Type Field 29 Label', 'ASGRPID', 'AttachmentSubmitter', 'Priority', 'AttachmentSourceGUID', 'Manufacturer (2)', 'WO Type Field 23', 'Product Name (2)', 'Product Model/Version (2)', 'WO Type Field 14 Label', 'WO Type Field 18', 'WO Type Field 28', 'z1D_Summary', 'WO Type Field 19 Label', 'zTmpEventGUID', 'Company3', 'Product Cat Tier 3 (2)', 'RequestCreatedFromDWP', 'CI_ReconId', 'Product Cat Tier 1(2)', 'Product Cat Tier 2 (2)', 'Customer Phone Number', 'AttachmentSourceFormName', 'Customer Last Name', 'Customer Company', 'Customer Person ID', 'Record ID', 'Customer First Name', 'Customer Internet E-mail', 'WO Type Field 13', 'Customer Organization', 'Previous_ServiceCI_ReconID', 'Customer Department', 'VIP', 'WO Type Field 25 Label', 'WO Type Field 30 Label', 'Last Name', 'First Name', 'Middle Initial', 'WO Type Field 2', 'Organization', 'WO Type Field 3', 'WO Type Field 1', 'WO Type Field 6', 'Support Organization', 'WO Type Field 7', 'Support Group Name', 'WO Type Field 4', 'WO Type Field 5', 'ClientLocale', 'WO Type Field 8', 'Description', 'WO Type Field 9', 'Location Company', 'z1D Char01', 'TemplateID', 'WO Type Field 24', 'Phone Number', 'Categorization Tier 1', 'Internet E-mail', 'WO Type Field 15 Label', 'WO Type Field 20 Label', 'Chg Location Address', 'WO Type Field 29', 'z1D_Activity Type*', 'z1D_SR_Instanceid', 'z1D Char07', 'z1D Char08', 'z1D_Command', 'Company', 'z1D Char05', 'Person ID', 'SRAttachment', 'z1D Char06', 'Add Request For:', 'z1D Action WO2', 'WO Type Field 19', 'Support Group ID', 'z1D_Action', 'z1D Integer01', 'z1D Char02', 'WO Type Field 14', 'z1D Char03', 'Categorization Tier 2', 'Categorization Tier 3', 'z1D_WorkInfoSubmitter', 'z1D Char04', 'Vendor Assignee Groups', 'Vendor Assignee Groups_parent', 'Region', 'z1D Integer02', 'z1D Integer03', 'Site Group', 'Department', 'SRID', 'z1D_Worklog Type', 'Business Service']
2025-06-26 12:43:55,730 - open.buscar_tickets - [32mINFO[0m - explorar_campos_disponibles:135 - Campos posibles para cédula en Work Orders: ['Request ID', 'Support Group ID 2', 'SRMSAOIGuid', 'Requested By Person ID', 'CI_DatasetId', 'Chat Session ID', 'SRInstanceID', 'CustomerFullName', 'InstanceId', 'SRMSRegistryInstanceID', 'Requestor ID', 'DWP_SRID', 'DWP_SRInstanceID', 'ASLOGID', 'Requestor_By_ID', 'Customer Middle Name', 'Work Order ID', 'WorkOrderID', 'ASGRPID', 'AttachmentSourceGUID', 'zTmpEventGUID', 'CI_ReconId', 'Customer Phone Number', 'Customer Last Name', 'Customer Company', 'Customer Person ID', 'Record ID', 'Customer First Name', 'Customer Internet E-mail', 'Customer Organization', 'Previous_ServiceCI_ReconID', 'Customer Department', 'Middle Initial', 'TemplateID', 'z1D_SR_Instanceid', 'Person ID', 'Support Group ID', 'SRID']
2025-06-26 12:43:55,730 - open.buscar_tickets - [32mINFO[0m - explorar_campos_disponibles:123 - Consultando Incidentes (HPD:IncidentInterface)...
2025-06-26 12:43:56,645 - open.buscar_tickets - [32mINFO[0m - explorar_campos_disponibles:131 - Campos disponibles en Incidentes: ['Request ID', 'Submitter', 'Submit Date', 'Assignee Login ID', 'Last Modified By', 'Last Modified Date', 'Status', 'policy_name', 'Created_By', 'Vendor Name', 'z1D_WorklogDetails', 'Resolution Category', 'z1D_CI_FormName', 'z1D_View_Access', 'Owner Support Company', 'Owner Group ID', 'Owner Group', 'Assignee Groups_parent', 'Auto Open Session', 'Impact_OR_Root', 'ServiceCI', 'HPD_CI_ReconID', 'ServiceCI_ReconID', 'Additional Access', 'Additional Access Parent', 'SRMSAOIGuid', 'Assignee Groups', 'Chat Session ID', 'z1D_ActivityDate_tab', 'HPD_CI_FormName', 'Site', 'Modified Chat Session ID', 'z1D_Secure_Log', 'COG_CognSuppGrpComp', 'Direct Contact Site ID', 'Associated Alarm', 'SRInstanceID', 'Direct Contact Mail Station', 'Direct Contact Location Details', 'Direct Contact Local Number', 'Direct Contact Extension', 'COG_CognSuppGrpOrg', 'Direct Contact Country Code', 'InfrastructureEventType', 'Direct Contact Area Code', 'COG_CognSuppGrpName', 'Direct Contact Country', 'Direct Contact State/Province', 'PortNumber', 'Direct Contact Street', 'Direct Contact Time Zone', 'Direct Contact Desk Location', 'Direct Contact City', 'FirstWIPDate', 'Direct Contact Zip/Postal Code', 'LastWIPDate', 'COG_CognSuppGrpID', 'Closure Manufacturer', 'z2AF_Act_Attachment_1', 'z1D_COG_AutoSuppGrpPredRule', 'Resolution Category Tier 3', 'Closure Product Category Tier1', 'Resolution Method', 'Resolution Category Tier 2', 'Closure Product Name', 'status_reason2', 'Closure Product Model/Version', 'Closure Product Category Tier2', 'HPD_CI', 'Closure Product Category Tier3', 'Assigned Group Shift Name', 'InstanceId', 'Assigned Group Shift ID', 'SRMS Registry Instance ID', 'z1D_ConfirmGroup', 'Owner Support Organization', 'z1D_CommunicationSource', 'z1D_CreatedFromBackEndSynchWI', 'TimeOfEvent', 'Broker Vendor Name', 'DWP_SRID', 'z1D_Details', 'NeedsAttentionCCS_Setting', 'z1D_Char02', 'DWP_SRInstanceID', 'AppPassword', 'Number of Attachments', 'cell_name', 'Direct Contact Organization', 'Direct Contact Department', 'Direct Contact Middle Initial', 'Direct Contact Phone Number', 'Direct Contact Site', 'Direct Contact Person ID', 'Resolution', 'Direct Contact Region', 'Customer Login ID', 'Direct Contact Site Group', 'Status_Reason', 'Direct Contact Last Name', 'Detailed Decription', 'Direct Contact First Name', 'Direct Contact Company', 'status_incident', 'policy_type', 'Needs Attention', 'Vendor Ticket Number', 'Estimated Resolution Date', 'root_incident_id_list', 'AppInterfaceForm', 'Total Transfers', 'Protocol', 'Priority Weight', 'Urgency', 'Direct Contact Login ID', 'Impact', 'Incident Number', 'Priority', 'mc_ueid', 'Assignee', 'AppInstanceServer', 'AttachmentSourceGUID', 'Assigned Group', 'Reported Source', 'Last _Assigned_Date', 'z1D_AssociationDescription', 'z1D_Summary', 'zTmpEventGUID', 'Assigned Support Company', 'RequestCreatedFromDWP', 'z1D_SV_AlarmId', 'AppLogin', 'AttachmentSourceFormName', 'Record ID', 'Direct Contact Internet E-mail', 'Previous_ServiceCI_ReconID', 'VIP', 'Contact Sensitivity', 'use_case', 'Required Resolution DateTime', 'Last Name', 'First Name', 'Component_ID', 'Contact Client Type', 'Middle Initial', 'Organization', 'AccessMode', 'TemplateID', 'Assigned Support Organization', 'ClientLocale', 'Country', 'State Province', 'Description', 'Company', 'z1D_COG_SuppGrpWorkInfoTag', 'City', 'z1D Char01', 'Phone Number', 'Product Model/Version', 'Product Name', 'Manufacturer', 'Categorization Tier 1', 'Last Acknowledged Date', 'Last Resolved Date', 'Internet E-mail', 'Reported Date', 'Responded Date', 'Corporate ID', 'Closed Date', 'z1D Permission Group ID', 'MaxRetries', 'z1D Permission Group List', 'Desk Location', 'Zip/Postal Code', 'z1D_Activity_Type', 'Mail Station', 'z1D_SR_Instanceid', 'Street', 'z1D_InterfaceAction', 'z1D_Command', 'KMSGUID', 'Contact Company', 'Person ID', 'Vendor Group ID', 'SRAttachment', 'Site ID', 'z1D_FormName', 'Vendor Organization', 'Assigned Group ID', 'Vendor Group', 'z1D Action', 'z1D Integer01', 'z1D Char02', 'z1D Char03', 'Direct Contact Corporate ID', 'Categorization Tier 2', 'Previous_HPD_CI_ReconID', 'Categorization Tier 3', 'bOrphanedRoot', 'z1D_WorkInfoSubmitter', 'z1D Char04', 'root_component_id_list', 'Vendor Assignee Groups', 'Vendor Assignee Groups_parent', 'Region', 'Product Categorization Tier 1', 'z1D Integer02', 'Site Group', 'Department', 'Product Categorization Tier 3', 'Product Categorization Tier 2', 'Entry ID', 'z1D Modify All Flag-V', 'Service Type', 'z1D Char27', 'SRID']
2025-06-26 12:43:56,646 - open.buscar_tickets - [32mINFO[0m - explorar_campos_disponibles:135 - Campos posibles para cédula en Incidentes: ['Request ID', 'Assignee Login ID', 'Owner Group ID', 'HPD_CI_ReconID', 'ServiceCI_ReconID', 'SRMSAOIGuid', 'Chat Session ID', 'Modified Chat Session ID', 'Direct Contact Site ID', 'SRInstanceID', 'Direct Contact Mail Station', 'Direct Contact Location Details', 'Direct Contact Local Number', 'Direct Contact Extension', 'Direct Contact Country Code', 'Direct Contact Area Code', 'Direct Contact Country', 'Direct Contact State/Province', 'Direct Contact Street', 'Direct Contact Time Zone', 'Direct Contact Desk Location', 'Direct Contact City', 'Direct Contact Zip/Postal Code', 'COG_CognSuppGrpID', 'InstanceId', 'Assigned Group Shift ID', 'SRMS Registry Instance ID', 'DWP_SRID', 'DWP_SRInstanceID', 'Direct Contact Organization', 'Direct Contact Department', 'Direct Contact Middle Initial', 'Direct Contact Phone Number', 'Direct Contact Site', 'Direct Contact Person ID', 'Direct Contact Region', 'Customer Login ID', 'Direct Contact Site Group', 'Direct Contact Last Name', 'Direct Contact First Name', 'Direct Contact Company', 'status_incident', 'root_incident_id_list', 'Direct Contact Login ID', 'Incident Number', 'mc_ueid', 'AttachmentSourceGUID', 'zTmpEventGUID', 'z1D_SV_AlarmId', 'Record ID', 'Direct Contact Internet E-mail', 'Previous_ServiceCI_ReconID', 'Contact Sensitivity', 'Component_ID', 'Contact Client Type', 'Middle Initial', 'TemplateID', 'Corporate ID', 'z1D Permission Group ID', 'z1D_SR_Instanceid', 'KMSGUID', 'Contact Company', 'Person ID', 'Vendor Group ID', 'Site ID', 'Assigned Group ID', 'Direct Contact Corporate ID', 'Previous_HPD_CI_ReconID', 'root_component_id_list', 'Entry ID', 'SRID']
2025-06-26 12:43:56,647 - open.buscar_tickets - [32mINFO[0m - probar_busqueda_general:62 - Probando búsqueda general para verificar datos...
2025-06-26 12:43:56,647 - open.buscar_tickets - [32mINFO[0m - probar_busqueda_general:74 - Consultando Work Orders sin filtros...
2025-06-26 12:43:57,116 - open.buscar_tickets - [32mINFO[0m - probar_busqueda_general:80 - Encontrados 5 registros en Work Orders
2025-06-26 12:43:57,116 - open.buscar_tickets - [32mINFO[0m - probar_busqueda_general:86 - Ejemplo 1 en Work Orders:
2025-06-26 12:43:57,117 - open.buscar_tickets - [32mINFO[0m - probar_busqueda_general:97 -   Work Order ID: WO0000000000200
2025-06-26 12:43:57,117 - open.buscar_tickets - [32mINFO[0m - probar_busqueda_general:97 -   Customer Person ID: PPL000000000110
2025-06-26 12:43:57,117 - open.buscar_tickets - [32mINFO[0m - probar_busqueda_general:97 -   Requestor ID: Lizeth.Sandoval
2025-06-26 12:43:57,117 - open.buscar_tickets - [32mINFO[0m - probar_busqueda_general:97 -   Customer First Name: Lizeth Juliana
2025-06-26 12:43:57,117 - open.buscar_tickets - [32mINFO[0m - probar_busqueda_general:97 -   Customer Last Name: Sandoval Diaz
2025-06-26 12:43:57,117 - open.buscar_tickets - [32mINFO[0m - probar_busqueda_general:97 -   Status: Cancelled
2025-06-26 12:43:57,117 - open.buscar_tickets - [32mINFO[0m - probar_busqueda_general:86 - Ejemplo 2 en Work Orders:
2025-06-26 12:43:57,117 - open.buscar_tickets - [32mINFO[0m - probar_busqueda_general:97 -   Work Order ID: WO0000000000201
2025-06-26 12:43:57,117 - open.buscar_tickets - [32mINFO[0m - probar_busqueda_general:97 -   Customer Person ID: PPL000000024205
2025-06-26 12:43:57,117 - open.buscar_tickets - [32mINFO[0m - probar_busqueda_general:97 -   Person ID: PPL000000000108
2025-06-26 12:43:57,117 - open.buscar_tickets - [32mINFO[0m - probar_busqueda_general:97 -   Requestor ID: dairheva
2025-06-26 12:43:57,117 - open.buscar_tickets - [32mINFO[0m - probar_busqueda_general:97 -   Customer First Name: Dairo
2025-06-26 12:43:57,117 - open.buscar_tickets - [32mINFO[0m - probar_busqueda_general:97 -   Customer Last Name: Herrera
2025-06-26 12:43:57,117 - open.buscar_tickets - [32mINFO[0m - probar_busqueda_general:97 -   Status: Closed
2025-06-26 12:43:57,118 - open.buscar_tickets - [32mINFO[0m - probar_busqueda_general:74 - Consultando Incidentes sin filtros...
2025-06-26 12:43:58,040 - open.buscar_tickets - [32mINFO[0m - probar_busqueda_general:80 - Encontrados 5 registros en Incidentes
2025-06-26 12:43:58,040 - open.buscar_tickets - [32mINFO[0m - probar_busqueda_general:86 - Ejemplo 1 en Incidentes:
2025-06-26 12:43:58,040 - open.buscar_tickets - [32mINFO[0m - probar_busqueda_general:97 -   Incident Number: INC000000000000
2025-06-26 12:43:58,040 - open.buscar_tickets - [32mINFO[0m - probar_busqueda_general:97 -   Status: Closed
2025-06-26 12:43:58,040 - open.buscar_tickets - [32mINFO[0m - probar_busqueda_general:86 - Ejemplo 2 en Incidentes:
2025-06-26 12:43:58,040 - open.buscar_tickets - [32mINFO[0m - probar_busqueda_general:97 -   Incident Number: INC000000000002
2025-06-26 12:43:58,040 - open.buscar_tickets - [32mINFO[0m - probar_busqueda_general:97 -   Status: Cancelled
2025-06-26 12:43:58,042 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:170 - Buscando tickets (Work Orders) para cédula 1143936436
2025-06-26 12:43:58,042 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:200 - URL: https://surasoporteti-qa-restapi.onbmc.com/api/arsys/v1/entry/WOI:WorkOrderInterface
2025-06-26 12:43:58,042 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:201 - Parámetros: {'q': '(\'Customer Person ID\'="1143936436" OR \'Person ID\'="1143936436" OR \'Requestor ID\'="1143936436" OR \'Customer Company\'="1143936436" OR \'Customer First Name\'="1143936436" OR \'Customer Last Name\'="1143936436")', 'limit': '1000'}
2025-06-26 12:43:58,043 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:202 - Headers: {'Authorization': 'AR-JWT eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIhPSF7ZW5jfSE9IUFBQUFETEVpaWU3NGRTcldJQ2xLWkptSGJYUDMvellONEtLWUNIY3lLUVF6M2RJRHltUmYwYTV3b1dIL1Y5a3lYbUxlZXc9PSIsIl9sb2NhbGVMYW5ndWFnZSI6ImVuIiwibmJmIjoxNzUwOTU5NzE1LCJpc3MiOiJwbGF0Zm9ybS1mdHMtMC5wbGF0Zm9ybS1mdHMiLCJfbG9jYWxlQ291bnRyeSI6IlVTIiwiX2F1dGhTdHJpbmciOiIhPSF7ZW5jfSE9IUFBQUFETEVpaWU3NGRTcldJQ2xLWlBzb013eTZyN3JYNnBxbVN3Nm9hR0U9IiwiZXhwIjoxNzUwOTg4NjM1LCJfY2FjaGVJZCI6MTk4MDkyMSwiaWF0IjoxNzUwOTU5ODM1LCJqdGkiOiJJREdIMDREWkMxVFJFQVNZN0ZKSFNZN0ZKSEY1RUsiLCJfYWJzb2x1dGVFeHBpcmF0aW9uVGltZSI6MTc1MTA0NjIzNX0.fk1_-HxIGAG21niBD6Pw7oqI0t79oPQs9h5LYW-7N54', 'Content-Type': 'application/json', 'Cookie': 'AR-JWT=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIhPSF7ZW5jfSE9IUFBQUFETEVpaWU3NGRTcldJQ2xLWkptSGJYUDMvellONEtLWUNIY3lLUVF6M2RJRHltUmYwYTV3b1dIL1Y5a3lYbUxlZXc9PSIsIl9sb2NhbGVMYW5ndWFnZSI6ImVuIiwibmJmIjoxNzUwOTU5NzE1LCJpc3MiOiJwbGF0Zm9ybS1mdHMtMC5wbGF0Zm9ybS1mdHMiLCJfbG9jYWxlQ291bnRyeSI6IlVTIiwiX2F1dGhTdHJpbmciOiIhPSF7ZW5jfSE9IUFBQUFETEVpaWU3NGRTcldJQ2xLWlBzb013eTZyN3JYNnBxbVN3Nm9hR0U9IiwiZXhwIjoxNzUwOTg4NjM1LCJfY2FjaGVJZCI6MTk4MDkyMSwiaWF0IjoxNzUwOTU5ODM1LCJqdGkiOiJJREdIMDREWkMxVFJFQVNZN0ZKSFNZN0ZKSEY1RUsiLCJfYWJzb2x1dGVFeHBpcmF0aW9uVGltZSI6MTc1MTA0NjIzNX0.fk1_-HxIGAG21niBD6Pw7oqI0t79oPQs9h5LYW-7N54'}
2025-06-26 12:43:58,448 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:206 - Status code work orders: 200
2025-06-26 12:43:58,448 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:213 - Respuesta work orders: {
  "entries": [],
  "numMatches": null,
  "_links": {
    "self": [
      {
        "href": "https://surasoporteti-qa-restapi.onbmc.com/api/arsys/v1/entry/WOI:WorkOrderInterface?q=%28%27Customer+Person+ID%27%3D%221143936436%22+OR+%27Person+ID%27%3D%221143936436%22+OR+%27Requestor+ID%27%3D%221143936436%22+OR+%27Customer+Company%27%3D%221143936436%22+OR+%27Customer+First+Name%27%3D%221143936436%22+OR+%27Customer+Last+Name%27%3D%221143936436%22%29&limit=1000"
      }
    ]
  }
}
2025-06-26 12:43:58,449 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:301 - Se encontraron 0 work orders
2025-06-26 12:43:58,449 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:304 - Buscando incidentes para cédula 1143936436
2025-06-26 12:43:58,449 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:315 - URL incidentes: https://surasoporteti-qa-restapi.onbmc.com/api/arsys/v1/entry/HPD:IncidentInterface
2025-06-26 12:43:58,449 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:316 - Parámetros incidentes: {'q': '\'Direct Contact Corporate ID\'="1143936436"', 'limit': '1000'}
2025-06-26 12:43:58,915 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:320 - Status code incidentes: 200
2025-06-26 12:43:58,916 - open.buscar_tickets - [32mINFO[0m - buscar_tickets_por_cedula:327 - Respuesta incidentes: {
  "entries": [],
  "numMatches": null,
  "_links": {
    "self": [
      {
        "href": "https://surasoporteti-qa-restapi.onbmc.com/api/arsys/v1/entry/HPD:IncidentInterface?q=%27Direct+Contact+Corporate+ID%27%3D%221143936436%22&limit=1000"
      }
    ]
  }
}
