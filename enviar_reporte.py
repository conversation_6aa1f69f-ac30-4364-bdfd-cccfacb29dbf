import json
import os
from datetime import datetime, timedelta
from mail import Mail

def formatear_fecha(fecha_str):
    """Formatea una fecha de formato ISO a formato DD/MM/YYYY HH:MM en hora colombiana (UTC-5)"""
    if not fecha_str or fecha_str == "N/A":
        return "N/A"

    try:
        # Parsear la fecha ISO (ej: 2025-02-19T07:00:08.000+0000)
        if 'T' in fecha_str:
            fecha_parte = fecha_str.split('T')[0]
            hora_parte = fecha_str.split('T')[1].split('.')[0] if '.' in fecha_str else fecha_str.split('T')[1].split('+')[0]

            # Convertir a datetime (asumiendo que viene en UTC)
            fecha_obj = datetime.strptime(f"{fecha_parte} {hora_parte}", "%Y-%m-%d %H:%M:%S")

            # Convertir a hora colombiana (UTC-5)
            fecha_colombia = fecha_obj - timedelta(hours=5)

            # Formatear como DD/MM/YYYY HH:MM (Hora Colombia)
            return fecha_colombia.strftime("%d/%m/%Y %H:%M (COL)")
        else:
            return fecha_str
    except Exception:
        return fecha_str

def es_nota_publica(nota):
    """Determina si una nota es pública basándose en ciertos criterios"""
    if not nota or not isinstance(nota, dict):
        return False
    
    # Criterios para considerar una nota como pública
    description = nota.get('description', '').lower()
    detailed_description = nota.get('detailed_description', '').lower()
    submitter = nota.get('submitter', '').lower()
    
    # Excluir notas internas o del sistema
    internal_keywords = ['interno', 'internal', 'sistema', 'system', 'automatico', 'automatic']
    
    # Si contiene palabras clave internas, no es pública
    for keyword in internal_keywords:
        if keyword in description or keyword in detailed_description:
            return False
    
    # Si el submitter es del sistema, probablemente no es pública
    system_submitters = ['sistema', 'system', 'automatico', 'automatic', 'bot']
    for system_sub in system_submitters:
        if system_sub in submitter:
            return False
    
    # Si tiene contenido real (no solo "sin resumen"), es pública
    if detailed_description and detailed_description not in ['sin descripción detallada', 'n/a', '']:
        return True
    
    return True  # Por defecto, considerar pública

def formatear_estado(status):
    """Aplica un estilo al estado según su valor"""
    if status and isinstance(status, str):
        if status.lower() in ['resuelto', 'resolved', 'closed', 'baja', 'low']:
            return f'<span style="color: green; font-weight: bold;">{status}</span>'
        elif status.lower() in ['pendiente', 'pending', 'media', 'medium']:
            return f'<span style="color: orange; font-weight: bold;">{status}</span>'
        elif status.lower() in ['abierto', 'assigned', 'open', 'alta', 'high']:
            return f'<span style="color: red; font-weight: bold;">{status}</span>'
    return status or "N/A"

def formatear_valor(value):
    """Formatea un valor para mejor visualización"""
    if value is None:
        return "N/A"
    elif isinstance(value, dict):
        return ", ".join([f"{k}: {v}" for k, v in value.items() if v is not None and v != "N/A"])
    elif value == "":
        return "N/A"
    return value

def crear_reporte_html(json_file):
    """Crea un reporte HTML a partir del archivo JSON unificado"""
    try:
        # Verificar si el archivo existe
        if not os.path.exists(json_file):
            return f"<p>Error: El archivo {json_file} no existe.</p>"
        
        # Cargar el archivo JSON
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Iniciar el HTML con estilos
        fecha_actual = datetime.now().strftime("%d/%m/%Y %H:%M:%S")
        html = f'''
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; color: #333; line-height: 1.6; }}
                .container {{ max-width: 800px; margin: 0 auto; padding: 20px; }}
                h1 {{ color: #2c5282; border-bottom: 2px solid #2c5282; padding-bottom: 10px; }}
                h2 {{ color: #3182ce; margin-top: 30px; }}
                h3 {{ color: #4299e1; margin-top: 20px; }}
                .info-panel {{ background-color: #f8f9fa; border-radius: 5px; padding: 15px; margin-bottom: 20px; border-left: 4px solid #3182ce; }}
                table {{ border-collapse: collapse; width: 100%; margin-bottom: 30px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); }}
                caption {{ font-weight: bold; font-size: 1.2em; padding: 10px; background-color: #e2e8f0; border-top: 2px solid #2c5282; border-left: 2px solid #2c5282; border-right: 2px solid #2c5282; }}
                th, td {{ border: 1px solid #dddddd; text-align: left; padding: 12px; }}
                th {{ background-color: #2c5282; color: white; font-weight: bold; }}
                tr:nth-child(even) {{ background-color: #f2f2f2; }}
                .note-table {{ margin-top: 10px; margin-bottom: 30px; width: 95%; margin-left: auto; margin-right: auto; }}
                .note-title {{ background-color: #e2e8f0; font-weight: bold; padding: 8px; border-top: 1px solid #cbd5e0; }}
                .footer {{ margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 0.9em; color: #666; text-align: center; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>Reporte Unificado de Tickets</h1>
                <p>Fecha de generación: {fecha_actual}</p>
        '''
        
        # Mostrar información de tickets
        if "tickets" in data and isinstance(data["tickets"], dict):
            tickets_info = data["tickets"]
            cedula = tickets_info.get("cedula", "N/A")
            fecha_consulta = tickets_info.get("fecha_consulta", "N/A")
            total_tickets = tickets_info.get("total_tickets", 0)
            tickets_procesados = tickets_info.get("tickets_procesados", 0)
            
            html += f'''
                <h2>Información de Ordenes de trabajo</h2>
                <div class="info-panel">
                    <p><strong>Cédula:</strong> {cedula}</p>
                    <p><strong>Fecha de consulta:</strong> {fecha_consulta}</p>
                    <p><strong>Total de tickets:</strong> {total_tickets}</p>
                    <p><strong>Tickets procesados:</strong> {tickets_procesados}</p>
                </div>
            '''
            
            # Mostrar tickets
            tickets = tickets_info.get("tickets", [])
            if tickets:
                html += f'''
                    <h3>Tickets ({len(tickets)})</h3>
                '''
                
                for i, ticket in enumerate(tickets):
                    request_id = ticket.get('request_id', 'Sin ID')
                    html += f'''
                    <table>
                        <caption>Ticket #{i+1}: {request_id}</caption>
                        <tr>
                            <th style="width: 30%;">Campo</th>
                            <th style="width: 70%;">Valor</th>
                        </tr>
                    '''
                    
                    # Campos específicos a mostrar según requerimientos EN EL ORDEN EXACTO
                    campos_mostrar = [
                        "dwp_number",           # DWP
                        "summary",              # Resumen  
                        "status",               # Estado
                        "detailed_description", # Descripción
                        "last_modified_date",   # Última modificación
                        "create_date",          # Fecha de creación
                        "customer",             # Nombre del cliente
                        "resolution"            # Resolución
                    ]
                    
                    # Mostrar solo los campos solicitados en el orden especificado
                    for key in campos_mostrar:
                        if key in ticket:
                            value = ticket[key]
                            formatted_value = formatear_valor(value)
                            
                            if key == "status":
                                formatted_value = formatear_estado(formatted_value)
                            elif key == "create_date" or key == "last_modified_date":
                                formatted_value = formatear_fecha(formatted_value)
                            
                            # Nombres de campos exactos según requerimientos
                            field_name = {
                                "dwp_number": "DWP",
                                "summary": "Resumen", 
                                "status": "Estado",
                                "detailed_description": "Descripción",
                                "last_modified_date": "Última modificación",
                                "create_date": "Fecha de creación",
                                "customer": "Nombre del cliente",
                                "resolution": "Resolución"
                            }.get(key, key.replace("_", " ").title())
                            
                            html += f'''
                            <tr>
                                <td><strong>{field_name}</strong></td>
                                <td>{formatted_value}</td>
                            </tr>
                            '''
                    
                    html += '''
                    </table>
                    '''
                    
                    # Si hay una nota, verificar si es pública antes de mostrarla
                    if "last_note" in ticket and ticket["last_note"] and es_nota_publica(ticket["last_note"]):
                        last_note = ticket["last_note"]
                        html += '''
                        <table class="note-table">
                            <tr>
                                <td class="note-title" colspan="2">Última nota</td>
                            </tr>
                        '''
                        
                        for key, value in last_note.items():
                            formatted_value = formatear_valor(value)
                            if key == "create_date" or key == "last_modified_date":
                                formatted_value = formatear_fecha(formatted_value)

                            # Traducir nombres de campos de notas al español
                            field_name = {
                                "note_number": "Número de Nota",
                                "description": "Resumen",
                                "detailed_description": "Descripción Detallada",
                                "last_modified_date": "Fecha de Modificación",
                                "create_date": "Fecha de Creación",
                                "submitter": "Enviado por"
                            }.get(key, key.replace("_", " ").title())

                            html += f'''
                            <tr>
                                <td style="width: 30%;"><strong>{field_name}</strong></td>
                                <td style="width: 70%;">{formatted_value}</td>
                            </tr>
                            '''
                        
                        html += '''
                        </table>
                        '''
            else:
                html += '''
                    <p style="color: #666; font-style: italic; text-align: center;">No hay tickets para mostrar</p>
                '''
        
        # Mostrar información de incidentes
        if "incidents" in data and isinstance(data["incidents"], dict):
            incidents_info = data["incidents"]
            login_id = incidents_info.get("login_id", "N/A")
            fecha_consulta = incidents_info.get("fecha_consulta", "N/A")
            total_incidentes = incidents_info.get("total_incidentes", 0)
            incidentes_procesados = incidents_info.get("incidentes_procesados", 0)
            
            html += f'''
                <h2>Información de Incidentes</h2>
                <div class="info-panel">
                    <p><strong>Login ID:</strong> {login_id}</p>
                    <p><strong>Fecha de consulta:</strong> {fecha_consulta}</p>
                    <p><strong>Total de incidentes:</strong> {total_incidentes}</p>
                    <p><strong>Incidentes procesados:</strong> {incidentes_procesados}</p>
                </div>
            '''
            
            # Mostrar incidentes
            incidentes = incidents_info.get("incidentes", [])
            if incidentes:
                html += f'''
                    <h3>Incidentes ({len(incidentes)})</h3>
                '''
                
                for i, incidente in enumerate(incidentes):
                    incident_id = incidente.get('incident_id', 'Sin ID')
                    html += f'''
                    <table>
                        <caption>Incidente #{i+1}: {incident_id}</caption>
                        <tr>
                            <th style="width: 30%;">Campo</th>
                            <th style="width: 70%;">Valor</th>
                        </tr>
                    '''
                    
                    # Campos específicos a mostrar según requerimientos EN EL ORDEN EXACTO
                    campos_mostrar = [
                        "dwp_number",           # DWP
                        "summary",              # Resumen  
                        "status",               # Estado
                        "detailed_description", # Descripción
                        "last_modified_date",   # Última modificación
                        "create_date",          # Fecha de creación
                        "customer",             # Nombre del cliente
                        "resolution"            # Resolución
                    ]
                    
                    # Mostrar solo los campos solicitados en el orden especificado
                    for key in campos_mostrar:
                        if key in incidente:
                            value = incidente[key]
                            formatted_value = formatear_valor(value)
                            
                            if key == "status":
                                formatted_value = formatear_estado(formatted_value)
                            elif key == "create_date" or key == "last_modified_date":
                                formatted_value = formatear_fecha(formatted_value)
                            
                            # Nombres de campos exactos según requerimientos
                            field_name = {
                                "dwp_number": "DWP",
                                "summary": "Resumen", 
                                "status": "Estado",
                                "detailed_description": "Descripción",
                                "last_modified_date": "Última modificación",
                                "create_date": "Fecha de creación",
                                "customer": "Nombre del cliente",
                                "resolution": "Resolución"
                            }.get(key, key.replace("_", " ").title())
                            
                            html += f'''
                            <tr>
                                <td><strong>{field_name}</strong></td>
                                <td>{formatted_value}</td>
                            </tr>
                            '''
                    
                    html += '''
                    </table>
                    '''
                    
                    # Si hay una nota, verificar si es pública antes de mostrarla
                    if "last_note" in incidente and incidente["last_note"] and es_nota_publica(incidente["last_note"]):
                        last_note = incidente["last_note"]
                        html += '''
                        <table class="note-table">
                            <tr>
                                <td class="note-title" colspan="2">Última nota</td>
                            </tr>
                        '''
                        
                        for key, value in last_note.items():
                            formatted_value = formatear_valor(value)
                            if key == "create_date" or key == "last_modified_date":
                                formatted_value = formatear_fecha(formatted_value)

                            # Traducir nombres de campos de notas al español
                            field_name = {
                                "note_number": "Número de Nota",
                                "description": "Resumen",
                                "detailed_description": "Descripción Detallada",
                                "last_modified_date": "Fecha de Modificación",
                                "create_date": "Fecha de Creación",
                                "submitter": "Enviado por"
                            }.get(key, key.replace("_", " ").title())

                            html += f'''
                            <tr>
                                <td style="width: 30%;"><strong>{field_name}</strong></td>
                                <td style="width: 70%;">{formatted_value}</td>
                            </tr>
                            '''
                        
                        html += '''
                        </table>
                        '''
            else:
                html += '''
                    <p style="color: #666; font-style: italic; text-align: center;">No hay incidentes para mostrar</p>
                '''
        
        # Cerrar el HTML
        html += '''
                <div class="footer">
                    <p>Este es un correo generado automáticamente, por favor no responda a este mensaje.</p>
                    <p>Departamento de TI - Arus</p>
                </div>
            </div>
        </body>
        </html>
        '''
        
        return html
        
    except json.JSONDecodeError:
        return "<p>Error: El archivo no es un JSON válido.</p>"
    except Exception as e:
        return f"<p>Error: {str(e)}</p>"

# Función principal para enviar el reporte
def enviar_reporte(json_file, destinatario=None):
    try:
        # Si no se especifica destinatario, usar el de .env
        if not destinatario:
            from dotenv import load_dotenv
            import os
            load_dotenv('c:\\Users\\<USER>\\Documents\\voice\\.env')
            destinatario = os.getenv('EMAIL_TO')
        
        # Generar el HTML del reporte
        mensaje_html = crear_reporte_html(json_file)
        
        # Enviar el correo
        mail = Mail(destinatario)
        mail.send(
            subject="Reporte Unificado de Tickets",
            message=mensaje_html
        )
        print(f"Reporte enviado correctamente a {destinatario}")
        return True
    except Exception as e:
        print(f"Error al enviar el reporte: {str(e)}")
        return False

if __name__ == "__main__":
    # Ruta al archivo JSON
    json_file = os.path.join(os.path.dirname(__file__), "reporte_unificado.json")
    
    # Enviar el reporte
    enviar_reporte(json_file)
