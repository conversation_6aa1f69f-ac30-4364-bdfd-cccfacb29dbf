#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Centralized logging configuration for BMC Remedy Container application.
Provides consistent logging across all modules with configurable output modes.
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Optional, Dict, Any


class LogLevel(Enum):
    """Enumeration for log levels."""
    DEBUG = logging.DEBUG
    INFO = logging.INFO
    WARNING = logging.WARNING
    ERROR = logging.ERROR
    CRITICAL = logging.CRITICAL


class OutputMode(Enum):
    """Enumeration for output modes."""
    CONSOLE = "console"
    FILE = "file"
    BOTH = "both"
    SILENT = "silent"


class ColoredFormatter(logging.Formatter):
    """Custom formatter with color support for console output."""
    
    # Color codes
    COLORS = {
        'DEBUG': '\033[36m',      # Cyan
        'INFO': '\033[32m',       # Green
        'WARNING': '\033[33m',    # Yellow
        'ERROR': '\033[31m',      # Red
        'CRITICAL': '\033[35m',   # Magenta
        'RESET': '\033[0m'        # Reset
    }
    
    def format(self, record):
        """Format log record with colors."""
        if hasattr(record, 'no_color') and record.no_color:
            return super().format(record)
            
        log_color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        record.levelname = f"{log_color}{record.levelname}{self.COLORS['RESET']}"
        return super().format(record)


class StructuredFormatter(logging.Formatter):
    """Formatter for structured logging output."""
    
    def format(self, record):
        """Format log record with structured data."""
        # Add structured data if available
        if hasattr(record, 'structured_data'):
            record.msg = f"{record.msg} | Data: {record.structured_data}"
        return super().format(record)


class LoggingConfig:
    """Centralized logging configuration manager."""
    
    def __init__(
        self,
        app_name: str = "bmc-remedy",
        log_level: LogLevel = LogLevel.INFO,
        output_mode: OutputMode = OutputMode.BOTH,
        log_dir: Optional[str] = None,
        max_file_size: int = 10 * 1024 * 1024,  # 10MB
        backup_count: int = 5,
        enable_colors: bool = True
    ):
        """
        Initialize logging configuration.
        
        Args:
            app_name: Name of the application for log files
            log_level: Minimum log level to capture
            output_mode: Where to send log output
            log_dir: Directory for log files (defaults to ./logs)
            max_file_size: Maximum size of log files before rotation
            backup_count: Number of backup log files to keep
            enable_colors: Whether to enable colored console output
        """
        self.app_name = app_name
        self.log_level = log_level
        self.output_mode = output_mode
        self.log_dir = Path(log_dir) if log_dir else Path("logs")
        self.max_file_size = max_file_size
        self.backup_count = backup_count
        self.enable_colors = enable_colors
        
        # Ensure log directory exists
        self.log_dir.mkdir(exist_ok=True)
        
        # Configure logging
        self._setup_logging()
    
    def _setup_logging(self):
        """Setup logging configuration."""
        # Clear any existing handlers
        root_logger = logging.getLogger()
        root_logger.handlers.clear()
        
        # Set root log level
        root_logger.setLevel(self.log_level.value)
        
        # Create formatters
        console_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        file_format = '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        
        if self.enable_colors and self.output_mode in [OutputMode.CONSOLE, OutputMode.BOTH]:
            console_formatter = ColoredFormatter(console_format)
        else:
            console_formatter = StructuredFormatter(console_format)
        
        file_formatter = StructuredFormatter(file_format)
        
        # Add console handler
        if self.output_mode in [OutputMode.CONSOLE, OutputMode.BOTH]:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(self.log_level.value)
            console_handler.setFormatter(console_formatter)
            root_logger.addHandler(console_handler)
        
        # Add file handler
        if self.output_mode in [OutputMode.FILE, OutputMode.BOTH]:
            log_file = self.log_dir / f"{self.app_name}.log"
            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=self.max_file_size,
                backupCount=self.backup_count,
                encoding='utf-8'
            )
            file_handler.setLevel(self.log_level.value)
            file_handler.setFormatter(file_formatter)
            root_logger.addHandler(file_handler)
        
        # Add null handler for silent mode
        if self.output_mode == OutputMode.SILENT:
            null_handler = logging.NullHandler()
            root_logger.addHandler(null_handler)
    
    def get_logger(self, name: str) -> logging.Logger:
        """
        Get a logger instance with the specified name.
        
        Args:
            name: Name for the logger
            
        Returns:
            Configured logger instance
        """
        return logging.getLogger(name)
    
    def log_structured(self, logger: logging.Logger, level: LogLevel, message: str, **kwargs):
        """
        Log a message with structured data.
        
        Args:
            logger: Logger instance to use
            level: Log level
            message: Log message
            **kwargs: Structured data to include
        """
        record = logger.makeRecord(
            logger.name, level.value, "", 0, message, (), None
        )
        record.structured_data = kwargs
        logger.handle(record)


# Global logging configuration instance
_logging_config: Optional[LoggingConfig] = None


def setup_logging(
    app_name: str = "bmc-remedy",
    log_level: str = "INFO",
    output_mode: str = "both",
    log_dir: Optional[str] = None,
    enable_colors: bool = True,
    **kwargs
) -> LoggingConfig:
    """
    Setup global logging configuration.
    
    Args:
        app_name: Name of the application
        log_level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        output_mode: Output mode (console, file, both, silent)
        log_dir: Directory for log files
        enable_colors: Whether to enable colored output
        **kwargs: Additional configuration options
        
    Returns:
        LoggingConfig instance
    """
    global _logging_config
    
    # Convert string parameters to enums
    level_enum = LogLevel[log_level.upper()]
    mode_enum = OutputMode[output_mode.upper()]
    
    _logging_config = LoggingConfig(
        app_name=app_name,
        log_level=level_enum,
        output_mode=mode_enum,
        log_dir=log_dir,
        enable_colors=enable_colors,
        **kwargs
    )
    
    return _logging_config


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance. If global config is not set, use default configuration.
    
    Args:
        name: Name for the logger
        
    Returns:
        Configured logger instance
    """
    global _logging_config
    
    if _logging_config is None:
        _logging_config = setup_logging()
    
    return _logging_config.get_logger(name)


def log_structured(logger: logging.Logger, level: str, message: str, **kwargs):
    """
    Log a message with structured data.

    Args:
        logger: Logger instance
        level: Log level as string
        message: Log message
        **kwargs: Structured data
    """
    global _logging_config

    if _logging_config is None:
        _logging_config = setup_logging()

    level_enum = LogLevel[level.upper()]
    _logging_config.log_structured(logger, level_enum, message, **kwargs)


# Convenience functions for common logging patterns
def log_operation_start(logger: logging.Logger, operation: str, **context):
    """Log the start of an operation with context."""
    log_structured(logger, "INFO", f"Starting {operation}", operation=operation, **context)


def log_operation_success(logger: logging.Logger, operation: str, **context):
    """Log successful completion of an operation."""
    log_structured(logger, "INFO", f"Successfully completed {operation}", operation=operation, **context)


def log_operation_error(logger: logging.Logger, operation: str, error: Exception, **context):
    """Log an error during an operation."""
    log_structured(
        logger, "ERROR",
        f"Error in {operation}: {str(error)}",
        operation=operation,
        error_type=type(error).__name__,
        error_message=str(error),
        **context
    )


def log_api_call(logger: logging.Logger, method: str, url: str, status_code: Optional[int] = None, **context):
    """Log an API call with details."""
    message = f"API {method} {url}"
    if status_code:
        message += f" -> {status_code}"

    level = "INFO" if not status_code or 200 <= status_code < 400 else "ERROR"
    log_structured(
        logger, level, message,
        api_method=method,
        api_url=url,
        status_code=status_code,
        **context
    )
